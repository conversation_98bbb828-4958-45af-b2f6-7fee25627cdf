build:
	go build -o fin_notebook  main.go

swagger:
	swag init

hot:
	docker compose -p fin_notebook -f docker-compose.yml up
	

hot-backend:
	@echo "Starting backend with Docker..."
	docker compose -p fin_notebook -f docker-compose.yml up

hot-frontend:
	@echo "Starting frontend development server..."
	cd ../frontend-web && npm start

serve:
	@echo "Building frontend and serving from backend..."
	@echo "Frontend will be built and copied to backend/dist"
	@echo "Backend will serve both API and frontend on http://localhost:8008"
	cd ../frontend-web && npm run build
	rm -rf ./dist/*
	cp -r ../frontend-web/build/* ./dist/
	@echo "Starting backend with static files..."
	DEV_MODE=true docker compose -p fin_notebook -f docker-compose.yml up

build-production:
	@echo "Building frontend for production..."
	@echo "API calls will use relative paths (/api/v1)"
	cd ../frontend-web && npm run build
	rm -rf ./dist/*
	cp -r ../frontend-web/build/* ./dist/
	@echo "Production build ready in ./dist/"
	@echo "Deploy this backend with the dist folder to your server"

run:
	docker compose -f docker-compose.yml up -d --build

down:
	docker compose -f docker-compose.yml down

mobile-up:
	cd ../butce360 && npm install && cd ios && pod install && cd .. &&  npx react-native run-ios

mobile-ipad:
	cd ../butce360 && npm install && cd ios && pod install && cd .. &&  npx react-native run-ios --simulator="iPad Pro (12.9-inch) (6th generation)"

mobile-pod:
	cd ../butce360/ios &&  pod install

mobile-node:
	cd ../butce360

mobile-build:
	@echo "🔄 Version artırılıyor..."
	cd ../butce360/ios && \
	CURRENT_VERSION=$$(grep -o 'MARKETING_VERSION = [0-9]*\.[0-9]*\.[0-9]*' butce360.xcodeproj/project.pbxproj | head -1 | grep -o '[0-9]*\.[0-9]*\.[0-9]*') && \
	NEW_VERSION=$$(echo $$CURRENT_VERSION | awk -F. '{$$NF = $$NF + 1;} 1' | sed 's/ /./g') && \
	echo "📱 Version: $$CURRENT_VERSION → $$NEW_VERSION" && \
	sed -i '' "s/MARKETING_VERSION = $$CURRENT_VERSION;/MARKETING_VERSION = $$NEW_VERSION;/g" butce360.xcodeproj/project.pbxproj && \
	echo "✅ Version güncellendi" && \
	xcodebuild archive \
	  -workspace butce360.xcworkspace \
	  -scheme butce360 \
	  -configuration Release \
	  -archivePath ./butce360.xcarchive && \
	xcodebuild -exportArchive \
	  -archivePath ./butce360.xcarchive \
	  -exportPath ./build \
	  -exportOptionsPlist ExportOptions.plist && \
	echo "🚀 Build tamamlandı: ./build/butce360.ipa"


mobile-ts-lint:
	cd ../butce360 && npx tsc --noEmit && npx eslint src/ --ext .ts,.tsx