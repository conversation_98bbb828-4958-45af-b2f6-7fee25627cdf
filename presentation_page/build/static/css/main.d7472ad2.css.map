{"version": 3, "file": "static/css/main.d7472ad2.css", "mappings": "AAAA,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,sCAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CACd,qBAAoB,CAApB,mDAAoB,EAApB,mDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EACpB,2BAAmB,CAAnB,2BAAmB,CAAnB,+BAAmB,CAAnB,eAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,mBAAmB,CAAnB,mBAAmB,CAAnB,oBAAmB,CAAnB,4BAAmB,CAAnB,oBAAmB,CAAnB,YAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,qBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,kCAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,sBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,8BAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,wCAAmB,CAAnB,gOAAmB,CAAnB,uCAAmB,CAAnB,8BAAmB,CAAnB,oNAAmB,CAAnB,wBAAmB,CAAnB,0DAAmB,CAAnB,+BAAmB,CAAnB,yBAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,oCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,gEAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,oHAAmB,CAAnB,oEAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,6BAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,kCAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,mCAAmB,CAAnB,8BAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,yCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,yCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,yCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,yCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,mCAAmB,CAAnB,iBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,oCAAmB,CAAnB,wCAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6FAAmB,CAAnB,qFAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,6EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,6EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,6EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,4EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,6EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,6EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,4EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,4EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,uEAAmB,CAAnB,yGAAmB,CAAnB,oEAAmB,CAAnB,oEAAmB,CAAnB,mEAAmB,CAAnB,oEAAmB,CAAnB,uEAAmB,CAAnB,uEAAmB,CAAnB,oEAAmB,CAAnB,sEAAmB,CAAnB,sEAAmB,CAAnB,mEAAmB,CAAnB,8DAAmB,CAAnB,0CAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,wBAAmB,CAAnB,kBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,8CAAmB,CAAnB,2CAAmB,CAAnB,4CAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,gDAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,yBAAmB,CAAnB,2BAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,mBAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,wBAAmB,CAAnB,aAAmB,CAAnB,2BAAmB,CAAnB,aAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,2BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,sCAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,2CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,6BAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,mDAAmB,CAAnB,4DAAmB,CAAnB,sEAAmB,CAAnB,kGAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,4CAAmB,CAAnB,sDAAmB,CAAnB,+CAAmB,CAAnB,kGAAmB,CAAnB,mDAAmB,CAAnB,oCAAmB,CAAnB,6BAAmB,CAAnB,kMAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,gLAAmB,CAAnB,+CAAmB,CAAnB,kTAAmB,CAAnB,sQAAmB,CAAnB,8CAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,6IAAmB,CAAnB,yFAAmB,CAAnB,uHAAmB,CAAnB,kDAAmB,CAAnB,0EAAmB,CAAnB,kDAAmB,CAAnB,4EAAmB,CAAnB,kDAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAEnB,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CAGA,oBACE,SACF,CAEA,0BACE,kBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CAGA,KACE,sBACF,CAGA,gBAcE,kBAAmB,CARnB,wBAAyB,CAEzB,kBAAmB,CAJnB,WAAY,CAOZ,2BAA4B,CAJ5B,UAAW,CAMX,YAAa,CAHb,cAAe,CAPf,WAAY,CAYZ,sBAAuB,CAdvB,cAAe,CAIf,UAAW,CAIX,iBAAkB,CAOlB,uBAAyB,CAdzB,UAAW,CAUX,WAKF,CAEA,sBACE,wBAAyB,CACzB,oBACF,CAEA,oBAEE,WAAY,CADZ,UAEF,CArEA,sDAsEA,CAtEA,uPAsEA,CAtEA,qDAsEA,CAtEA,sDAsEA,CAtEA,oBAsEA,CAtEA,wDAsEA,CAtEA,sDAsEA,CAtEA,oBAsEA,CAtEA,wDAsEA,CAtEA,sDAsEA,CAtEA,oBAsEA,CAtEA,uDAsEA,CAtEA,mDAsEA,CAtEA,oBAsEA,CAtEA,wDAsEA,CAtEA,mDAsEA,CAtEA,oBAsEA,CAtEA,wDAsEA,CAtEA,8CAsEA,CAtEA,wBAsEA,CAtEA,sDAsEA,CAtEA,8CAsEA,CAtEA,wBAsEA,CAtEA,qDAsEA,CAtEA,2CAsEA,CAtEA,wBAsEA,CAtEA,wDAsEA,CAtEA,0CAsEA,CAtEA,wBAsEA,CAtEA,wDAsEA,CAtEA,4CAsEA,CAtEA,wBAsEA,CAtEA,sDAsEA,CAtEA,wCAsEA,CAtEA,qBAsEA,CAtEA,wDAsEA,CAtEA,0FAsEA,CAtEA,yDAsEA,CAtEA,iEAsEA,CAtEA,oFAsEA,CAtEA,+CAsEA,CAtEA,aAsEA,CAtEA,6CAsEA,CAtEA,kDAsEA,CAtEA,aAsEA,CAtEA,+CAsEA,CAtEA,kDAsEA,CAtEA,aAsEA,CAtEA,6CAsEA,CAtEA,kDAsEA,CAtEA,aAsEA,CAtEA,4CAsEA,CAtEA,gDAsEA,CAtEA,aAsEA,CAtEA,6CAsEA,CAtEA,iDAsEA,CAtEA,aAsEA,CAtEA,8CAsEA,CAtEA,4CAsEA,CAtEA,UAsEA,CAtEA,+CAsEA,CAtEA,gEAsEA,CAtEA,4DAsEA,CAtEA,gGAsEA,CAtEA,kGAsEA,CAtEA,uFAsEA,CAtEA,iGAsEA,CAtEA,wFAsEA,CAtEA,kGAsEA,CAtEA,+CAsEA,CAtEA,kGAsEA,CAtEA,gEAsEA,CAtEA,oCAsEA,CAtEA,6DAsEA,CAtEA,oCAsEA,CAtEA,sDAsEA,CAtEA,oBAsEA,CAtEA,uDAsEA,CAtEA,+HAsEA,CAtEA,wGAsEA,CAtEA,uEAsEA,CAtEA,wFAsEA,CAtEA,kDAsEA,CAtEA,wDAsEA,CAtEA,iEAsEA,CAtEA,2QAsEA,CAtEA,qDAsEA,CAtEA,gBAsEA,CAtEA,+DAsEA,CAtEA,aAsEA,CAtEA,6CAsEA,CAtEA,yDAsEA,EAtEA,kDAsEA,CAtEA,8DAsEA,CAtEA,8DAsEA,CAtEA,gCAsEA,EAtEA,8CAsEA,CAtEA,oBAsEA,CAtEA,yCAsEA,CAtEA,wBAsEA,CAtEA,sBAsEA,CAtEA,wBAsEA,CAtEA,8DAsEA,CAtEA,8DAsEA,CAtEA,8DAsEA,CAtEA,8BAsEA,CAtEA,gBAsEA,CAtEA,+BAsEA,CAtEA,kBAsEA,CAtEA,4BAsEA,CAtEA,aAsEA,CAtEA,+BAsEA,CAtEA,aAsEA,CAtEA,4BAsEA,CAtEA,aAsEA,ECtEA,KACE,iBACF,CAEA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YACE,wBAAyB,CAEzB,UAAY,CADZ,YAEF,CAEA,UACE,aACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAGA,oBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,kBACE,+BACF,CAEA,iBACE,MACE,SACF,CACA,IACE,UACF,CACF,CAEA,eACE,mDACF", "sources": ["index.css", "App.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #10b981;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #059669;\n}\n\n/* Smooth scrolling */\nhtml {\n  scroll-behavior: smooth;\n}\n\n/* WhatsApp floating button */\n.whatsapp-float {\n  position: fixed;\n  width: 60px;\n  height: 60px;\n  bottom: 40px;\n  right: 40px;\n  background-color: #25d366;\n  color: #FFF;\n  border-radius: 50px;\n  text-align: center;\n  font-size: 30px;\n  box-shadow: 2px 2px 3px #999;\n  z-index: 100;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.whatsapp-float:hover {\n  background-color: #128c7e;\n  transform: scale(1.1);\n}\n\n.whatsapp-float svg {\n  width: 30px;\n  height: 30px;\n}\n", ".App {\n  text-align: center;\n}\n\n.App-logo {\n  height: 40vmin;\n  pointer-events: none;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  .App-logo {\n    animation: App-logo-spin infinite 20s linear;\n  }\n}\n\n.App-header {\n  background-color: #282c34;\n  padding: 20px;\n  color: white;\n}\n\n.App-link {\n  color: #61dafb;\n}\n\n@keyframes App-logo-spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* Custom animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.animate-fadeInUp {\n  animation: fadeInUp 0.6s ease-out;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: .5;\n  }\n}\n\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n"], "names": [], "sourceRoot": ""}