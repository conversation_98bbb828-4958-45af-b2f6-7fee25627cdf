{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/nocytech/butce360/presentation_page/src/App.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';\nimport './App.css';\nimport Header from './components/Header';\nimport Hero from './components/Hero';\nimport Features from './components/Features';\nimport HowItWorks from './components/HowItWorks';\nimport Pricing from './components/Pricing';\nimport Contact from './components/Contact';\nimport Footer from './components/Footer';\n\n// Pages\nimport WhatIsPage from './pages/WhatIsPage';\nimport HowItWorksPage from './pages/HowItWorksPage';\nimport PricingPage from './pages/PricingPage';\nimport ContactPage from './pages/ContactPage';\nimport FAQPage from './pages/FAQPage';\n\n// ScrollToTop component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ScrollToTop() {\n  _s();\n  const {\n    pathname\n  } = useLocation();\n  useEffect(() => {\n    window.scrollTo(0, 0);\n  }, [pathname]);\n  return null;\n}\n_s(ScrollToTop, \"+8VPq4+XDMjo/kjL3WLkbwU2Amg=\", false, function () {\n  return [useLocation];\n});\n_c = ScrollToTop;\nfunction App() {\n  _s2();\n  const [language, setLanguage] = useState('tr');\n  const toggleLanguage = () => {\n    setLanguage(language === 'tr' ? 'en' : 'tr');\n  };\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: [/*#__PURE__*/_jsxDEV(ScrollToTop, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        language: language,\n        toggleLanguage: toggleLanguage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Hero, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Features, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(HowItWorks, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Pricing, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/nedir\",\n          element: /*#__PURE__*/_jsxDEV(WhatIsPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/nasil-calisir\",\n          element: /*#__PURE__*/_jsxDEV(HowItWorksPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 49\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/ucretlendirme\",\n          element: /*#__PURE__*/_jsxDEV(PricingPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 49\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/iletisim\",\n          element: /*#__PURE__*/_jsxDEV(ContactPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/sss\",\n          element: /*#__PURE__*/_jsxDEV(FAQPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"https://wa.me/905417173986\",\n        className: \"whatsapp-float\",\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        title: \"WhatsApp ile \\u0130leti\\u015Fim\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n}\n_s2(App, \"sdQwLqKPpQmdoviV+biTZAI5BPA=\");\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"ScrollToTop\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "useLocation", "Header", "Hero", "Features", "HowItWorks", "Pricing", "Contact", "Footer", "WhatIsPage", "HowItWorksPage", "PricingPage", "ContactPage", "FAQPage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ScrollToTop", "_s", "pathname", "window", "scrollTo", "_c", "App", "_s2", "language", "setLanguage", "toggleLanguage", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "path", "element", "href", "target", "rel", "title", "fill", "viewBox", "d", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/nocytech/butce360/presentation_page/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';\nimport './App.css';\nimport Header from './components/Header';\nimport Hero from './components/Hero';\nimport Features from './components/Features';\nimport HowItWorks from './components/HowItWorks';\nimport Pricing from './components/Pricing';\nimport Contact from './components/Contact';\nimport Footer from './components/Footer';\n\n// Pages\nimport WhatIsPage from './pages/WhatIsPage';\nimport HowItWorksPage from './pages/HowItWorksPage';\nimport PricingPage from './pages/PricingPage';\nimport ContactPage from './pages/ContactPage';\nimport FAQPage from './pages/FAQPage';\n\n// ScrollToTop component\nfunction ScrollToTop() {\n  const { pathname } = useLocation();\n\n  useEffect(() => {\n    window.scrollTo(0, 0);\n  }, [pathname]);\n\n  return null;\n}\n\nfunction App() {\n  const [language, setLanguage] = useState('tr');\n\n  const toggleLanguage = () => {\n    setLanguage(language === 'tr' ? 'en' : 'tr');\n  };\n\n  return (\n    <Router>\n      <ScrollToTop />\n      <div className=\"App\">\n        <Header language={language} toggleLanguage={toggleLanguage} />\n        <Routes>\n          <Route path=\"/\" element={\n            <>\n              <Hero />\n              <Features />\n              <HowItWorks />\n              <Pricing />\n              <Contact />\n            </>\n          } />\n          <Route path=\"/nedir\" element={<WhatIsPage />} />\n          <Route path=\"/nasil-calisir\" element={<HowItWorksPage />} />\n          <Route path=\"/ucretlendirme\" element={<PricingPage />} />\n          <Route path=\"/iletisim\" element={<ContactPage />} />\n          <Route path=\"/sss\" element={<FAQPage />} />\n        </Routes>\n        <Footer />\n\n        {/* WhatsApp Floating Button */}\n        <a\n          href=\"https://wa.me/905417173986\"\n          className=\"whatsapp-float\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          title=\"WhatsApp ile İletişim\"\n        >\n          <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"/>\n          </svg>\n        </a>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,WAAW,QAAQ,kBAAkB;AACtF,OAAO,WAAW;AAClB,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;;AAExC;AACA,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,OAAO,MAAM,iBAAiB;;AAErC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAS,CAAC,GAAGnB,WAAW,CAAC,CAAC;EAElCL,SAAS,CAAC,MAAM;IACdyB,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;EAEd,OAAO,IAAI;AACb;AAACD,EAAA,CARQD,WAAW;EAAA,QACGjB,WAAW;AAAA;AAAAsB,EAAA,GADzBL,WAAW;AAUpB,SAASM,GAAGA,CAAA,EAAG;EAAAC,GAAA;EACb,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAE9C,MAAMiC,cAAc,GAAGA,CAAA,KAAM;IAC3BD,WAAW,CAACD,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;EAC9C,CAAC;EAED,oBACEX,OAAA,CAACjB,MAAM;IAAA+B,QAAA,gBACLd,OAAA,CAACG,WAAW;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACflB,OAAA;MAAKmB,SAAS,EAAC,KAAK;MAAAL,QAAA,gBAClBd,OAAA,CAACb,MAAM;QAACwB,QAAQ,EAAEA,QAAS;QAACE,cAAc,EAAEA;MAAe;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9DlB,OAAA,CAAChB,MAAM;QAAA8B,QAAA,gBACLd,OAAA,CAACf,KAAK;UAACmC,IAAI,EAAC,GAAG;UAACC,OAAO,eACrBrB,OAAA,CAAAE,SAAA;YAAAY,QAAA,gBACEd,OAAA,CAACZ,IAAI;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACRlB,OAAA,CAACX,QAAQ;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACZlB,OAAA,CAACV,UAAU;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACdlB,OAAA,CAACT,OAAO;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXlB,OAAA,CAACR,OAAO;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,eACX;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACJlB,OAAA,CAACf,KAAK;UAACmC,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAErB,OAAA,CAACN,UAAU;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDlB,OAAA,CAACf,KAAK;UAACmC,IAAI,EAAC,gBAAgB;UAACC,OAAO,eAAErB,OAAA,CAACL,cAAc;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DlB,OAAA,CAACf,KAAK;UAACmC,IAAI,EAAC,gBAAgB;UAACC,OAAO,eAAErB,OAAA,CAACJ,WAAW;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDlB,OAAA,CAACf,KAAK;UAACmC,IAAI,EAAC,WAAW;UAACC,OAAO,eAAErB,OAAA,CAACH,WAAW;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDlB,OAAA,CAACf,KAAK;UAACmC,IAAI,EAAC,MAAM;UAACC,OAAO,eAAErB,OAAA,CAACF,OAAO;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACTlB,OAAA,CAACP,MAAM;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGVlB,OAAA;QACEsB,IAAI,EAAC,4BAA4B;QACjCH,SAAS,EAAC,gBAAgB;QAC1BI,MAAM,EAAC,QAAQ;QACfC,GAAG,EAAC,qBAAqB;QACzBC,KAAK,EAAC,iCAAuB;QAAAX,QAAA,eAE7Bd,OAAA;UAAK0B,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAb,QAAA,eAC1Cd,OAAA;YAAM4B,CAAC,EAAC;UAAklC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzlC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACR,GAAA,CA7CQD,GAAG;AAAAoB,GAAA,GAAHpB,GAAG;AA+CZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAqB,GAAA;AAAAC,YAAA,CAAAtB,EAAA;AAAAsB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}