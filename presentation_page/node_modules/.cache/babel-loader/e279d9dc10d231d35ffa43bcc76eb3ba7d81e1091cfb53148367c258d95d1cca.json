{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/nocytech/butce360/presentation_page/src/components/HowItWorks.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HowItWorks = () => {\n  const steps = [{\n    step: '1',\n    title: 'Hesap Oluşturun',\n    description: '<PERSON><PERSON><PERSON>lı ve kolay kayıt işlemi ile hesabınızı oluşturun',\n    icon: '👤'\n  }, {\n    step: '2',\n    title: 'He<PERSON><PERSON><PERSON><PERSON>n<PERSON><PERSON><PERSON> Ekleyin',\n    description: '<PERSON>a hesapları, kredi kartları ve nakit hesaplarınızı sisteme ekleyin',\n    icon: '🏦'\n  }, {\n    step: '3',\n    title: '<PERSON>şlem<PERSON>inizi Kaydedin',\n    description: '<PERSON><PERSON><PERSON> ve giderlerinizi manuel olarak girin veya banka ekstresini yükleyin',\n    icon: '📝'\n  }, {\n    step: '4',\n    title: '<PERSON><PERSON><PERSON> & <PERSON>ı<PERSON>',\n    description: 'Detaylı raporları inceleyin ve gelecek için bütçe planları oluşturun',\n    icon: '📊'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-20 bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-block bg-white rounded-full px-6 py-2 mb-6 shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600 font-semibold text-sm\",\n            children: \"NASIL \\xC7ALI\\u015EIR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\",\n          children: \"Nas\\u0131l \\xC7al\\u0131\\u015F\\u0131r?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"Butce360 ile finansal takip sadece 4 ad\\u0131mda tamamlan\\u0131r. Basit ve kullan\\u0131c\\u0131 dostu aray\\xFCz\\xFCm\\xFCz ile dakikalar i\\xE7inde ba\\u015Flayabilirsiniz.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\",\n        children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center group\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-20 h-20 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\",\n              children: step.step\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this), index < steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden lg:block absolute top-10 left-1/2 w-full h-0.5 bg-gradient-to-r from-emerald-300 to-emerald-400 transform translate-x-10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl mb-4\",\n            children: step.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors\",\n            children: step.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 leading-relaxed\",\n            children: step.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-3xl p-12 shadow-lg border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-3xl font-bold text-gray-900 mb-6\",\n          children: \"Hemen Ba\\u015Flay\\u0131n!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 mb-8 max-w-2xl mx-auto\",\n          children: \"Finansal \\xF6zg\\xFCrl\\xFC\\u011F\\xFCn\\xFCze giden yolculuk sadece birka\\xE7 t\\u0131k uza\\u011F\\u0131n\\u0131zda. Bug\\xFCn ba\\u015Flay\\u0131n, yar\\u0131n fark\\u0131 g\\xF6r\\xFCn.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://app.butce360.com/register\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-8 py-4 rounded-2xl font-bold hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-emerald-500/25\",\n            children: \"\\xDCcretsiz Hesap Olu\\u015Ftur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/iletisim\",\n            className: \"border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-2xl font-bold hover:border-emerald-500 hover:text-emerald-600 transition-colors\",\n            children: \"Daha Fazla Bilgi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_c = HowItWorks;\nexport default HowItWorks;\nvar _c;\n$RefreshReg$(_c, \"HowItWorks\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "HowItWorks", "steps", "step", "title", "description", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "index", "length", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/nocytech/butce360/presentation_page/src/components/HowItWorks.js"], "sourcesContent": ["import React from 'react';\n\nconst HowItWorks = () => {\n  const steps = [\n    {\n      step: '1',\n      title: '<PERSON><PERSON><PERSON>',\n      description: 'H<PERSON><PERSON>lı ve kolay kayıt işlemi ile hesabınızı oluşturun',\n      icon: '👤'\n    },\n    {\n      step: '2',\n      title: 'He<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z<PERSON> Ekleyin',\n      description: '<PERSON><PERSON> hesapları, kredi kartları ve nakit hesaplarınızı sisteme ekleyin',\n      icon: '🏦'\n    },\n    {\n      step: '3',\n      title: 'İşlemlerinizi Kaydedin',\n      description: '<PERSON><PERSON><PERSON> ve giderlerinizi manuel olarak girin veya banka ekstresini yükleyin',\n      icon: '📝'\n    },\n    {\n      step: '4',\n      title: '<PERSON><PERSON>z <PERSON>in & Planlayın',\n      description: 'Detaylı raporları inceleyin ve gelecek için bütçe planları oluşturun',\n      icon: '📊'\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-block bg-white rounded-full px-6 py-2 mb-6 shadow-sm\">\n            <span className=\"text-gray-600 font-semibold text-sm\">NASIL ÇALIŞIR</span>\n          </div>\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n            Nasıl Çalışır?\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Butce360 ile finansal takip sadece 4 adımda tamamlanır. \n            Basit ve kullanıcı dostu arayüzümüz ile dakikalar içinde başlayabilirsiniz.\n          </p>\n        </div>\n\n        {/* Steps */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\">\n          {steps.map((step, index) => (\n            <div key={index} className=\"text-center group\">\n              {/* Step Number */}\n              <div className=\"relative mb-8\">\n                <div className=\"w-20 h-20 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  {step.step}\n                </div>\n                {/* Connector Line */}\n                {index < steps.length - 1 && (\n                  <div className=\"hidden lg:block absolute top-10 left-1/2 w-full h-0.5 bg-gradient-to-r from-emerald-300 to-emerald-400 transform translate-x-10\"></div>\n                )}\n              </div>\n\n              {/* Icon */}\n              <div className=\"text-4xl mb-4\">{step.icon}</div>\n\n              {/* Content */}\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors\">\n                {step.title}\n              </h3>\n              <p className=\"text-gray-600 leading-relaxed\">\n                {step.description}\n              </p>\n            </div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center bg-white rounded-3xl p-12 shadow-lg border border-gray-100\">\n          <h3 className=\"text-3xl font-bold text-gray-900 mb-6\">\n            Hemen Başlayın!\n          </h3>\n          <p className=\"text-lg text-gray-600 mb-8 max-w-2xl mx-auto\">\n            Finansal özgürlüğünüze giden yolculuk sadece birkaç tık uzağınızda.\n            Bugün başlayın, yarın farkı görün.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a\n              href=\"https://app.butce360.com/register\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-8 py-4 rounded-2xl font-bold hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-emerald-500/25\"\n            >\n              Ücretsiz Hesap Oluştur\n            </a>\n            <a\n              href=\"/iletisim\"\n              className=\"border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-2xl font-bold hover:border-emerald-500 hover:text-emerald-600 transition-colors\"\n            >\n              Daha Fazla Bilgi\n            </a>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HowItWorks;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,MAAMC,KAAK,GAAG,CACZ;IACEC,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,sDAAsD;IACnEC,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,wEAAwE;IACrFC,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,2EAA2E;IACxFC,IAAI,EAAE;EACR,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,sEAAsE;IACnFC,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACEN,OAAA;IAASO,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eACnCR,OAAA;MAAKO,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErCR,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCR,OAAA;UAAKO,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1ER,OAAA;YAAMO,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eACNZ,OAAA;UAAIO,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAElE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLZ,OAAA;UAAGO,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNZ,OAAA;QAAKO,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACxEN,KAAK,CAACW,GAAG,CAAC,CAACV,IAAI,EAAEW,KAAK,kBACrBd,OAAA;UAAiBO,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE5CR,OAAA;YAAKO,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BR,OAAA;cAAKO,SAAS,EAAC,6MAA6M;cAAAC,QAAA,EACzNL,IAAI,CAACA;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,EAELE,KAAK,GAAGZ,KAAK,CAACa,MAAM,GAAG,CAAC,iBACvBf,OAAA;cAAKO,SAAS,EAAC;YAAiI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACvJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNZ,OAAA;YAAKO,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEL,IAAI,CAACG;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGhDZ,OAAA;YAAIO,SAAS,EAAC,qFAAqF;YAAAC,QAAA,EAChGL,IAAI,CAACC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACLZ,OAAA;YAAGO,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EACzCL,IAAI,CAACE;UAAW;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA,GArBIE,KAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNZ,OAAA;QAAKO,SAAS,EAAC,wEAAwE;QAAAC,QAAA,gBACrFR,OAAA;UAAIO,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLZ,OAAA;UAAGO,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAG5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJZ,OAAA;UAAKO,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DR,OAAA;YACEgB,IAAI,EAAC,mCAAmC;YACxCC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBX,SAAS,EAAC,2MAA2M;YAAAC,QAAA,EACtN;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJZ,OAAA;YACEgB,IAAI,EAAC,WAAW;YAChBT,SAAS,EAAC,0IAA0I;YAAAC,QAAA,EACrJ;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACO,EAAA,GAvGIlB,UAAU;AAyGhB,eAAeA,UAAU;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}