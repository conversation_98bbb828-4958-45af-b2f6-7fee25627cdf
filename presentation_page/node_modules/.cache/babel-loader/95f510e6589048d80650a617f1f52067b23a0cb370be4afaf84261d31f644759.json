{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/nocytech/butce360/presentation_page/src/pages/WhatIsPage.js\";\nimport React from 'react';\nimport Features from '../components/Features';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WhatIsPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gradient-to-br from-emerald-50 to-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl lg:text-6xl font-bold text-gray-900 mb-6\",\n            children: [\"Butce360\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-emerald-600 block\",\n              children: \"Nedir?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 13,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n            children: \"Butce360, ki\\u015Fisel ve kurumsal finansal y\\xF6netimi kolayla\\u015Ft\\u0131ran, yapay zeka destekli ak\\u0131ll\\u0131 b\\xFCt\\xE7e takip platformudur.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-6xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-6\",\n                children: \"Finansal \\xD6zg\\xFCrl\\xFC\\u011F\\xFCn\\xFCze Giden Yol\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg text-gray-600 mb-6 leading-relaxed\",\n                children: \"Butce360, gelir ve giderlerinizi takip etmenizi, b\\xFCt\\xE7e planlamas\\u0131 yapman\\u0131z\\u0131 ve finansal hedeflerinize ula\\u015Fman\\u0131z\\u0131 sa\\u011Flayan kapsaml\\u0131 bir platformdur.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg text-gray-600 mb-8 leading-relaxed\",\n                children: \"Banka ekstrelerinizi otomatik olarak analiz eder, harcama al\\u0131\\u015Fkanl\\u0131klar\\u0131n\\u0131z\\u0131 raporlar ve size ki\\u015Fiselle\\u015Ftirilmi\\u015F finansal \\xF6neriler sunar.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-emerald-500 mr-3\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 43,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 42,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-700 font-medium\",\n                    children: \"Otomatik i\\u015Flem kategorilendirme\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 45,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-emerald-500 mr-3\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 49,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 48,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-700 font-medium\",\n                    children: \"Ak\\u0131ll\\u0131 b\\xFCt\\xE7e \\xF6nerileri\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 51,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 47,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-emerald-500 mr-3\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 55,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 54,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-700 font-medium\",\n                    children: \"Detayl\\u0131 finansal raporlar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 57,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-br from-emerald-100 to-blue-100 rounded-3xl p-8 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl mb-4\",\n                  children: \"\\uD83D\\uDCCA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold text-gray-900 mb-4\",\n                  children: \"Ak\\u0131ll\\u0131 Analiz\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Yapay zeka algoritmalar\\u0131 ile harcama al\\u0131\\u015Fkanl\\u0131klar\\u0131n\\u0131z\\u0131 analiz eder, tasarruf f\\u0131rsatlar\\u0131n\\u0131 ke\\u015Ffeder.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-6xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-6\",\n              children: \"Neden Butce360?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n              children: \"Finansal y\\xF6netimde kar\\u015F\\u0131la\\u015Ft\\u0131\\u011F\\u0131n\\u0131z zorluklar\\u0131 \\xE7\\xF6zen, hayat\\u0131n\\u0131z\\u0131 kolayla\\u015Ft\\u0131ran \\xF6zellikler.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-4\",\n                children: \"\\u23F1\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-4\",\n                children: \"Zaman Tasarrufu\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Manuel hesap tutma derdine son. Otomatik kategorilendirme ile saatlerce zaman kazan\\u0131n.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-4\",\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-4\",\n                children: \"Hedef Odakl\\u0131\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Finansal hedeflerinizi belirleyin, ilerlemenizi takip edin ve hedefinize ula\\u015F\\u0131n.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-4\",\n                children: \"\\uD83D\\uDCF1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-4\",\n                children: \"Her Yerden Eri\\u015Fim\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Web ve mobil uygulamalar ile finansal durumunuzu her yerden kontrol edin.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-4\",\n                children: \"\\uD83D\\uDD10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-4\",\n                children: \"G\\xFCvenli\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Banka seviyesinde g\\xFCvenlik ile verileriniz tamamen korunur. KVKK uyumlu veri i\\u015Fleme.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-4\",\n                children: \"\\uD83D\\uDCC8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-4\",\n                children: \"B\\xFCy\\xFCme Odakl\\u0131\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Sadece takip de\\u011Fil, finansal b\\xFCy\\xFCmenizi destekleyen \\xF6neriler ve stratejiler.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-4\",\n                children: \"\\uD83E\\uDD1D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-4\",\n                children: \"Uzman Deste\\u011Fi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Finansal dan\\u0131\\u015Fmanlar\\u0131m\\u0131zdan destek al\\u0131n, do\\u011Fru kararlar verin.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Features, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gradient-to-br from-emerald-600 to-blue-600 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl lg:text-4xl font-bold mb-6\",\n          children: \"Finansal \\xD6zg\\xFCrl\\xFC\\u011F\\xFCn\\xFCze Ba\\u015Flay\\u0131n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl mb-8 max-w-2xl mx-auto opacity-90\",\n          children: \"Bug\\xFCn Butce360'a kat\\u0131l\\u0131n ve finansal gelece\\u011Finizi \\u015Fekillendirmeye ba\\u015Flay\\u0131n.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://app.butce360.com/register\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"bg-white text-emerald-600 px-8 py-4 rounded-2xl font-bold hover:bg-gray-100 transition-colors shadow-lg\",\n            children: \"\\xDCcretsiz Ba\\u015Fla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/iletisim\",\n            className: \"border-2 border-white text-white px-8 py-4 rounded-2xl font-bold hover:bg-white hover:text-emerald-600 transition-colors\",\n            children: \"Demo \\u0130ste\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = WhatIsPage;\nexport default WhatIsPage;\nvar _c;\n$RefreshReg$(_c, \"WhatIsPage\");", "map": {"version": 3, "names": ["React", "Features", "jsxDEV", "_jsxDEV", "WhatIsPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "viewBox", "fillRule", "d", "clipRule", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/nocytech/butce360/presentation_page/src/pages/WhatIsPage.js"], "sourcesContent": ["import React from 'react';\nimport Features from '../components/Features';\n\nconst WhatIsPage = () => {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Section */}\n      <section className=\"py-20 bg-gradient-to-br from-emerald-50 to-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h1 className=\"text-4xl lg:text-6xl font-bold text-gray-900 mb-6\">\n              Butce360\n              <span className=\"text-emerald-600 block\">Nedir?</span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 leading-relaxed\">\n              Butce360, kişisel ve kurumsal finansal yönetimi kolaylaştıran, \n              yapay zeka destekli akıllı bütçe takip platformudur.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* What is Butce360 */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n              <div>\n                <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-6\">\n                  Finansal Özgürlüğünüze Giden Yol\n                </h2>\n                <p className=\"text-lg text-gray-600 mb-6 leading-relaxed\">\n                  Butce360, gelir ve giderlerinizi takip etmenizi, bütçe planlaması yapmanızı ve \n                  finansal hedeflerinize ulaşmanızı sağlayan kapsamlı bir platformdur.\n                </p>\n                <p className=\"text-lg text-gray-600 mb-8 leading-relaxed\">\n                  Banka ekstrelerinizi otomatik olarak analiz eder, harcama alışkanlıklarınızı \n                  raporlar ve size kişiselleştirilmiş finansal öneriler sunar.\n                </p>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center\">\n                    <svg className=\"w-6 h-6 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span className=\"text-gray-700 font-medium\">Otomatik işlem kategorilendirme</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <svg className=\"w-6 h-6 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span className=\"text-gray-700 font-medium\">Akıllı bütçe önerileri</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <svg className=\"w-6 h-6 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span className=\"text-gray-700 font-medium\">Detaylı finansal raporlar</span>\n                  </div>\n                </div>\n              </div>\n              <div className=\"relative\">\n                <div className=\"bg-gradient-to-br from-emerald-100 to-blue-100 rounded-3xl p-8 text-center\">\n                  <div className=\"text-6xl mb-4\">📊</div>\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">Akıllı Analiz</h3>\n                  <p className=\"text-gray-600\">\n                    Yapay zeka algoritmaları ile harcama alışkanlıklarınızı analiz eder, \n                    tasarruf fırsatlarını keşfeder.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Key Benefits */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-6\">\n                Neden Butce360?\n              </h2>\n              <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n                Finansal yönetimde karşılaştığınız zorlukları çözen, \n                hayatınızı kolaylaştıran özellikler.\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              <div className=\"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\">\n                <div className=\"text-4xl mb-4\">⏱️</div>\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Zaman Tasarrufu</h3>\n                <p className=\"text-gray-600\">\n                  Manuel hesap tutma derdine son. Otomatik kategorilendirme ile \n                  saatlerce zaman kazanın.\n                </p>\n              </div>\n\n              <div className=\"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\">\n                <div className=\"text-4xl mb-4\">🎯</div>\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Hedef Odaklı</h3>\n                <p className=\"text-gray-600\">\n                  Finansal hedeflerinizi belirleyin, ilerlemenizi takip edin ve \n                  hedefinize ulaşın.\n                </p>\n              </div>\n\n              <div className=\"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\">\n                <div className=\"text-4xl mb-4\">📱</div>\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Her Yerden Erişim</h3>\n                <p className=\"text-gray-600\">\n                  Web ve mobil uygulamalar ile finansal durumunuzu her yerden \n                  kontrol edin.\n                </p>\n              </div>\n\n              <div className=\"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\">\n                <div className=\"text-4xl mb-4\">🔐</div>\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Güvenli</h3>\n                <p className=\"text-gray-600\">\n                  Banka seviyesinde güvenlik ile verileriniz tamamen korunur. \n                  KVKK uyumlu veri işleme.\n                </p>\n              </div>\n\n              <div className=\"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\">\n                <div className=\"text-4xl mb-4\">📈</div>\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Büyüme Odaklı</h3>\n                <p className=\"text-gray-600\">\n                  Sadece takip değil, finansal büyümenizi destekleyen öneriler \n                  ve stratejiler.\n                </p>\n              </div>\n\n              <div className=\"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\">\n                <div className=\"text-4xl mb-4\">🤝</div>\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Uzman Desteği</h3>\n                <p className=\"text-gray-600\">\n                  Finansal danışmanlarımızdan destek alın, doğru kararlar verin.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Component */}\n      <Features />\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-gradient-to-br from-emerald-600 to-blue-600 text-white\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold mb-6\">\n            Finansal Özgürlüğünüze Başlayın\n          </h2>\n          <p className=\"text-xl mb-8 max-w-2xl mx-auto opacity-90\">\n            Bugün Butce360'a katılın ve finansal geleceğinizi şekillendirmeye başlayın.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a\n              href=\"https://app.butce360.com/register\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"bg-white text-emerald-600 px-8 py-4 rounded-2xl font-bold hover:bg-gray-100 transition-colors shadow-lg\"\n            >\n              Ücretsiz Başla\n            </a>\n            <a\n              href=\"/iletisim\"\n              className=\"border-2 border-white text-white px-8 py-4 rounded-2xl font-bold hover:bg-white hover:text-emerald-600 transition-colors\"\n            >\n              Demo İste\n            </a>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default WhatIsPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,oBACED,OAAA;IAAKE,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpCH,OAAA;MAASE,SAAS,EAAC,kDAAkD;MAAAC,QAAA,eACnEH,OAAA;QAAKE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCH,OAAA;UAAKE,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CH,OAAA;YAAIE,SAAS,EAAC,mDAAmD;YAAAC,QAAA,GAAC,UAEhE,eAAAH,OAAA;cAAME,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACLP,OAAA;YAAGE,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAG1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAASE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCH,OAAA;QAAKE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCH,OAAA;UAAKE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCH,OAAA;YAAKE,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClEH,OAAA;cAAAG,QAAA,gBACEH,OAAA;gBAAIE,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAC;cAElE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLP,OAAA;gBAAGE,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAG1D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJP,OAAA;gBAAGE,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAG1D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJP,OAAA;gBAAKE,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBH,OAAA;kBAAKE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCH,OAAA;oBAAKE,SAAS,EAAC,+BAA+B;oBAACM,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAN,QAAA,eACpFH,OAAA;sBAAMU,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,uIAAuI;sBAACC,QAAQ,EAAC;oBAAS;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrL,CAAC,eACNP,OAAA;oBAAME,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eACNP,OAAA;kBAAKE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCH,OAAA;oBAAKE,SAAS,EAAC,+BAA+B;oBAACM,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAN,QAAA,eACpFH,OAAA;sBAAMU,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,uIAAuI;sBAACC,QAAQ,EAAC;oBAAS;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrL,CAAC,eACNP,OAAA;oBAAME,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACNP,OAAA;kBAAKE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCH,OAAA;oBAAKE,SAAS,EAAC,+BAA+B;oBAACM,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAN,QAAA,eACpFH,OAAA;sBAAMU,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,uIAAuI;sBAACC,QAAQ,EAAC;oBAAS;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrL,CAAC,eACNP,OAAA;oBAAME,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNP,OAAA;cAAKE,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBH,OAAA;gBAAKE,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,gBACzFH,OAAA;kBAAKE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvCP,OAAA;kBAAIE,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxEP,OAAA;kBAAGE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAG7B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAASE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCH,OAAA;QAAKE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCH,OAAA;UAAKE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCH,OAAA;YAAKE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCH,OAAA;cAAIE,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLP,OAAA;cAAGE,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAGvD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENP,OAAA;YAAKE,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnEH,OAAA;cAAKE,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBACxEH,OAAA;gBAAKE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCP,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzEP,OAAA;gBAAGE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAG7B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENP,OAAA;cAAKE,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBACxEH,OAAA;gBAAKE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCP,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtEP,OAAA;gBAAGE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAG7B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENP,OAAA;cAAKE,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBACxEH,OAAA;gBAAKE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCP,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3EP,OAAA;gBAAGE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAG7B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENP,OAAA;cAAKE,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBACxEH,OAAA;gBAAKE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCP,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjEP,OAAA;gBAAGE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAG7B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENP,OAAA;cAAKE,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBACxEH,OAAA;gBAAKE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCP,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvEP,OAAA;gBAAGE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAG7B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENP,OAAA;cAAKE,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBACxEH,OAAA;gBAAKE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCP,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvEP,OAAA;gBAAGE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAE7B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA,CAACF,QAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGZP,OAAA;MAASE,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAClFH,OAAA;QAAKE,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDH,OAAA;UAAIE,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLP,OAAA;UAAGE,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA;UAAKE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DH,OAAA;YACEa,IAAI,EAAC,mCAAmC;YACxCC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBb,SAAS,EAAC,yGAAyG;YAAAC,QAAA,EACpH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJP,OAAA;YACEa,IAAI,EAAC,WAAW;YAChBX,SAAS,EAAC,0HAA0H;YAAAC,QAAA,EACrI;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACS,EAAA,GAhLIf,UAAU;AAkLhB,eAAeA,UAAU;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}