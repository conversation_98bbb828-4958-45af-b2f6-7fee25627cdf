{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/nocytech/butce360/presentation_page/src/components/Features.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Features = () => {\n  const features = [{\n    icon: '💰',\n    title: '<PERSON><PERSON>r & Gider Takibi',\n    description: 'Tüm gelir ve giderlerinizi kategorilere ayırarak detaylı takip edin',\n    color: 'emerald'\n  }, {\n    icon: '📊',\n    title: 'Akıllı Raporlama',\n    description: 'Harcama alışkanlıklarınızı analiz eden detaylı raporlar ve grafikler',\n    color: 'blue'\n  }, {\n    icon: '🎯',\n    title: 'Bütçe Planlama',\n    description: 'Kategorilere göre bütçe belirleyin ve hedeflerinizi takip edin',\n    color: 'purple'\n  }, {\n    icon: '🏦',\n    title: 'Çoklu Hesap Yönetimi',\n    description: '<PERSON>a hesapları, kredi kartlar<PERSON> ve nakit hesaplarınızı tek yerden yönetin',\n    color: 'orange'\n  }, {\n    icon: '📄',\n    title: 'Banka Ekstresi Analizi',\n    description: 'PDF banka ekstrelerinizi yükleyin, otomatik olarak işlemlerinizi kategorize edin',\n    color: 'pink'\n  }, {\n    icon: '🔒',\n    title: 'Güvenli Veri Saklama',\n    description: 'Finansal verileriniz SSL şifreleme ile güvenli şekilde korunur',\n    color: 'indigo'\n  }];\n  const getColorClasses = color => {\n    const colors = {\n      emerald: 'bg-emerald-50 border-emerald-200 text-emerald-600',\n      blue: 'bg-blue-50 border-blue-200 text-blue-600',\n      purple: 'bg-purple-50 border-purple-200 text-purple-600',\n      orange: 'bg-orange-50 border-orange-200 text-orange-600',\n      pink: 'bg-pink-50 border-pink-200 text-pink-600',\n      indigo: 'bg-indigo-50 border-indigo-200 text-indigo-600'\n    };\n    return colors[color] || colors.emerald;\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-24 bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-20\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-block bg-gray-100 rounded-full px-6 py-2 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600 font-semibold text-sm\",\n            children: \"\\xD6ZELL\\u0130KLER\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl lg:text-6xl font-black text-gray-900 mb-6 leading-tight\",\n          children: [\"Finansal \\xD6zg\\xFCrl\\xFCk,\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"block text-emerald-600\",\n            children: \"Kolay Y\\xF6netim\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n          children: \"Butce360 ile finansal hayat\\u0131n\\u0131z\\u0131 kontrol alt\\u0131na al\\u0131n. Ak\\u0131ll\\u0131 ara\\xE7lar, basit aray\\xFCz, g\\xFC\\xE7l\\xFC analiz.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\",\n        children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"group bg-white border border-gray-100 rounded-3xl p-8 hover:shadow-2xl hover:shadow-gray-200/50 transition-all duration-500 hover:-translate-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `inline-flex items-center justify-center w-16 h-16 rounded-2xl border-2 mb-6 text-2xl ${getColorClasses(feature.color)}`,\n            children: feature.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 mb-4 group-hover:text-emerald-600 transition-colors\",\n            children: feature.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 leading-relaxed\",\n            children: feature.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-gradient-to-br from-emerald-50 to-blue-50 rounded-3xl p-12 border border-emerald-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-3xl lg:text-4xl font-black text-gray-900 mb-6\",\n          children: \"Finansal Gelece\\u011Finizi \\u015Eekillendirin!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 mb-8 max-w-2xl mx-auto\",\n          children: \"Binlerce kullan\\u0131c\\u0131 Butce360 ile finansal hedeflerine ula\\u015Ft\\u0131. Siz de bu ba\\u015Far\\u0131ya ortak olun.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://app.butce360.com/register\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"bg-emerald-600 text-white px-8 py-4 rounded-2xl font-bold hover:bg-emerald-700 transition-colors shadow-lg hover:shadow-emerald-500/25\",\n            children: \"\\xDCcretsiz Dene\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/iletisim\",\n            className: \"border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-2xl font-bold hover:border-gray-400 hover:bg-white transition-colors\",\n            children: \"Demo \\u0130ste\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_c = Features;\nexport default Features;\nvar _c;\n$RefreshReg$(_c, \"Features\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Features", "features", "icon", "title", "description", "color", "getColorClasses", "colors", "emerald", "blue", "purple", "orange", "pink", "indigo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "feature", "index", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/nocytech/butce360/presentation_page/src/components/Features.js"], "sourcesContent": ["import React from 'react';\n\nconst Features = () => {\n  const features = [\n    {\n      icon: '💰',\n      title: '<PERSON><PERSON><PERSON> & Gider Takibi',\n      description: 'Tüm gelir ve giderlerinizi kategorilere ayırarak detaylı takip edin',\n      color: 'emerald'\n    },\n    {\n      icon: '📊',\n      title: 'Akıllı Raporlama',\n      description: 'Harcama alışkanlıklarınızı analiz eden detaylı raporlar ve grafikler',\n      color: 'blue'\n    },\n    {\n      icon: '🎯',\n      title: 'Bütçe Planlama',\n      description: 'Kategorilere göre bütçe belirleyin ve hedeflerinizi takip edin',\n      color: 'purple'\n    },\n    {\n      icon: '🏦',\n      title: 'Çoklu Hesap Yönetimi',\n      description: 'Banka hesapları, kredi kartları ve nakit hesaplarınızı tek yerden yönetin',\n      color: 'orange'\n    },\n    {\n      icon: '📄',\n      title: 'Banka Ekstresi Analizi',\n      description: 'PDF banka ekstrelerinizi yükleyin, otomatik olarak işlemlerinizi kategorize edin',\n      color: 'pink'\n    },\n    {\n      icon: '🔒',\n      title: 'Güvenli Veri Saklama',\n      description: 'Finansal verileriniz SSL şifreleme ile güvenli şekilde korunur',\n      color: 'indigo'\n    }\n  ];\n\n  const getColorClasses = (color) => {\n    const colors = {\n      emerald: 'bg-emerald-50 border-emerald-200 text-emerald-600',\n      blue: 'bg-blue-50 border-blue-200 text-blue-600',\n      purple: 'bg-purple-50 border-purple-200 text-purple-600',\n      orange: 'bg-orange-50 border-orange-200 text-orange-600',\n      pink: 'bg-pink-50 border-pink-200 text-pink-600',\n      indigo: 'bg-indigo-50 border-indigo-200 text-indigo-600'\n    };\n    return colors[color] || colors.emerald;\n  };\n\n  return (\n    <section className=\"py-24 bg-white\">\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <div className=\"text-center mb-20\">\n          <div className=\"inline-block bg-gray-100 rounded-full px-6 py-2 mb-6\">\n            <span className=\"text-gray-600 font-semibold text-sm\">ÖZELLİKLER</span>\n          </div>\n          <h2 className=\"text-4xl lg:text-6xl font-black text-gray-900 mb-6 leading-tight\">\n            Finansal Özgürlük,\n            <span className=\"block text-emerald-600\">Kolay Yönetim</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Butce360 ile finansal hayatınızı kontrol altına alın.\n            Akıllı araçlar, basit arayüz, güçlü analiz.\n          </p>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\">\n          {features.map((feature, index) => (\n            <div\n              key={index}\n              className=\"group bg-white border border-gray-100 rounded-3xl p-8 hover:shadow-2xl hover:shadow-gray-200/50 transition-all duration-500 hover:-translate-y-2\"\n            >\n              <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl border-2 mb-6 text-2xl ${getColorClasses(feature.color)}`}>\n                {feature.icon}\n              </div>\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4 group-hover:text-emerald-600 transition-colors\">\n                {feature.title}\n              </h3>\n              <p className=\"text-gray-600 leading-relaxed\">\n                {feature.description}\n              </p>\n            </div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center bg-gradient-to-br from-emerald-50 to-blue-50 rounded-3xl p-12 border border-emerald-100\">\n          <h3 className=\"text-3xl lg:text-4xl font-black text-gray-900 mb-6\">\n            Finansal Geleceğinizi Şekillendirin!\n          </h3>\n          <p className=\"text-lg text-gray-600 mb-8 max-w-2xl mx-auto\">\n            Binlerce kullanıcı Butce360 ile finansal hedeflerine ulaştı.\n            Siz de bu başarıya ortak olun.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a\n              href=\"https://app.butce360.com/register\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"bg-emerald-600 text-white px-8 py-4 rounded-2xl font-bold hover:bg-emerald-700 transition-colors shadow-lg hover:shadow-emerald-500/25\"\n            >\n              Ücretsiz Dene\n            </a>\n            <a\n              href=\"/iletisim\"\n              className=\"border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-2xl font-bold hover:border-gray-400 hover:bg-white transition-colors\"\n            >\n              Demo İste\n            </a>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Features;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,qEAAqE;IAClFC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,sEAAsE;IACnFC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,gEAAgE;IAC7EC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,2EAA2E;IACxFC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,kFAAkF;IAC/FC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,gEAAgE;IAC7EC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,eAAe,GAAID,KAAK,IAAK;IACjC,MAAME,MAAM,GAAG;MACbC,OAAO,EAAE,mDAAmD;MAC5DC,IAAI,EAAE,0CAA0C;MAChDC,MAAM,EAAE,gDAAgD;MACxDC,MAAM,EAAE,gDAAgD;MACxDC,IAAI,EAAE,0CAA0C;MAChDC,MAAM,EAAE;IACV,CAAC;IACD,OAAON,MAAM,CAACF,KAAK,CAAC,IAAIE,MAAM,CAACC,OAAO;EACxC,CAAC;EAED,oBACET,OAAA;IAASe,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eACjChB,OAAA;MAAKe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErChB,OAAA;QAAKe,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChChB,OAAA;UAAKe,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eACnEhB,OAAA;YAAMe,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACNpB,OAAA;UAAIe,SAAS,EAAC,kEAAkE;UAAAC,QAAA,GAAC,6BAE/E,eAAAhB,OAAA;YAAMe,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACLpB,OAAA;UAAGe,SAAS,EAAC,yDAAyD;UAAAC,QAAA,EAAC;QAGvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNpB,OAAA;QAAKe,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACxEd,QAAQ,CAACmB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BvB,OAAA;UAEEe,SAAS,EAAC,kJAAkJ;UAAAC,QAAA,gBAE5JhB,OAAA;YAAKe,SAAS,EAAE,wFAAwFR,eAAe,CAACe,OAAO,CAAChB,KAAK,CAAC,EAAG;YAAAU,QAAA,EACtIM,OAAO,CAACnB;UAAI;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNpB,OAAA;YAAIe,SAAS,EAAC,qFAAqF;YAAAC,QAAA,EAChGM,OAAO,CAAClB;UAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACLpB,OAAA;YAAGe,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EACzCM,OAAO,CAACjB;UAAW;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA,GAXCG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNpB,OAAA;QAAKe,SAAS,EAAC,qGAAqG;QAAAC,QAAA,gBAClHhB,OAAA;UAAIe,SAAS,EAAC,oDAAoD;UAAAC,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpB,OAAA;UAAGe,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAG5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJpB,OAAA;UAAKe,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DhB,OAAA;YACEwB,IAAI,EAAC,mCAAmC;YACxCC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBX,SAAS,EAAC,wIAAwI;YAAAC,QAAA,EACnJ;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJpB,OAAA;YACEwB,IAAI,EAAC,WAAW;YAChBT,SAAS,EAAC,+HAA+H;YAAAC,QAAA,EAC1I;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACO,EAAA,GAvHI1B,QAAQ;AAyHd,eAAeA,QAAQ;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}