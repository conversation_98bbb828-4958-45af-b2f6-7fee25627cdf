{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/nocytech/butce360/presentation_page/src/pages/HowItWorksPage.js\";\nimport React from 'react';\nimport HowItWorks from '../components/HowItWorks';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HowItWorksPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gradient-to-br from-emerald-50 to-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl lg:text-6xl font-bold text-gray-900 mb-6\",\n            children: [\"Butce360\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-emerald-600 block\",\n              children: \"Nas\\u0131l \\xC7al\\u0131\\u015F\\u0131r?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 13,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n            children: \"Sadece 4 basit ad\\u0131mda finansal takibi ba\\u015Flat\\u0131n. Teknik bilgi gerektirmez, dakikalar i\\xE7inde kullanmaya ba\\u015Flay\\u0131n.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HowItWorks, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-6xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-6\",\n              children: \"Detayl\\u0131 Ad\\u0131mlar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n              children: \"Her ad\\u0131mda size rehberlik ediyoruz. Kolay kurulum, h\\u0131zl\\u0131 ba\\u015Flang\\u0131\\xE7.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4\",\n                    children: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 44,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-2xl font-bold text-gray-900\",\n                    children: \"Hesap Olu\\u015Fturun\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 47,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 43,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg text-gray-600 mb-6\",\n                  children: \"Email adresiniz ile h\\u0131zl\\u0131 kay\\u0131t olun. Sadece birka\\xE7 dakika s\\xFCrer ve kredi kart\\u0131 bilgisi gerektirmez.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 49,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-emerald-500 mr-3\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 56,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 55,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700\",\n                      children: \"Email do\\u011Frulama\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 58,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 54,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-emerald-500 mr-3\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 62,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 61,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700\",\n                      children: \"G\\xFCvenli \\u015Fifre olu\\u015Fturma\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 64,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 60,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-emerald-500 mr-3\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 68,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 67,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700\",\n                      children: \"Profil bilgileri\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 70,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 66,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-br from-emerald-100 to-blue-100 rounded-3xl p-8 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl mb-4\",\n                  children: \"\\uD83D\\uDC64\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-xl font-bold text-gray-900 mb-2\",\n                  children: \"H\\u0131zl\\u0131 Kay\\u0131t\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"2 dakikada hesab\\u0131n\\u0131z haz\\u0131r\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"order-2 lg:order-1\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl p-8 text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-6xl mb-4\",\n                    children: \"\\uD83C\\uDFE6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-xl font-bold text-gray-900 mb-2\",\n                    children: \"G\\xFCvenli Ba\\u011Flant\\u0131\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 86,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Banka seviyesinde g\\xFCvenlik\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"order-1 lg:order-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4\",\n                    children: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 92,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-2xl font-bold text-gray-900\",\n                    children: \"Hesaplar\\u0131n\\u0131z\\u0131 Ekleyin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 95,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg text-gray-600 mb-6\",\n                  children: \"Banka hesaplar\\u0131n\\u0131z\\u0131, kredi kartlar\\u0131n\\u0131z\\u0131 ve nakit hesaplar\\u0131n\\u0131z\\u0131 sisteme ekleyin. T\\xFCm finansal durumunuzu tek yerden g\\xF6r\\xFCn.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-emerald-500 mr-3\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 104,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 103,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700\",\n                      children: \"Banka hesaplar\\u0131\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 106,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-emerald-500 mr-3\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 110,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 109,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700\",\n                      children: \"Kredi kartlar\\u0131\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 112,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-emerald-500 mr-3\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 116,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 115,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700\",\n                      children: \"Nakit hesaplar\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 118,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4\",\n                    children: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-2xl font-bold text-gray-900\",\n                    children: \"\\u0130\\u015Flemlerinizi Kaydedin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg text-gray-600 mb-6\",\n                  children: \"Gelir ve giderlerinizi manuel olarak girin veya banka ekstresini y\\xFCkleyin. Sistem otomatik olarak kategorilere ay\\u0131r\\u0131r.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-emerald-500 mr-3\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 140,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 139,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700\",\n                      children: \"Manuel i\\u015Flem giri\\u015Fi\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 142,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-emerald-500 mr-3\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 146,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 145,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700\",\n                      children: \"PDF ekstres y\\xFCkleme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 148,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-emerald-500 mr-3\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 152,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 151,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700\",\n                      children: \"Otomatik kategorilendirme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-br from-purple-100 to-pink-100 rounded-3xl p-8 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl mb-4\",\n                  children: \"\\uD83D\\uDCDD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-xl font-bold text-gray-900 mb-2\",\n                  children: \"Kolay Giri\\u015F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Otomatik kategorilendirme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"order-2 lg:order-1\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gradient-to-br from-orange-100 to-red-100 rounded-3xl p-8 text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-6xl mb-4\",\n                    children: \"\\uD83D\\uDCCA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-xl font-bold text-gray-900 mb-2\",\n                    children: \"Ak\\u0131ll\\u0131 Analiz\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Detayl\\u0131 raporlar ve \\xF6neriler\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"order-1 lg:order-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4\",\n                    children: \"4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-2xl font-bold text-gray-900\",\n                    children: \"Analiz Edin & Planlay\\u0131n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg text-gray-600 mb-6\",\n                  children: \"Detayl\\u0131 raporlar\\u0131 inceleyin, harcama al\\u0131\\u015Fkanl\\u0131klar\\u0131n\\u0131z\\u0131 analiz edin ve gelecek i\\xE7in ak\\u0131ll\\u0131 b\\xFCt\\xE7e planlar\\u0131 olu\\u015Fturun.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-emerald-500 mr-3\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 188,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700\",\n                      children: \"Detayl\\u0131 raporlar\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-emerald-500 mr-3\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 194,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700\",\n                      children: \"B\\xFCt\\xE7e planlama\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-emerald-500 mr-3\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 200,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700\",\n                      children: \"Finansal \\xF6neriler\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gradient-to-br from-emerald-600 to-blue-600 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl lg:text-4xl font-bold mb-6\",\n          children: \"Hemen Ba\\u015Flay\\u0131n!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl mb-8 max-w-2xl mx-auto opacity-90\",\n          children: \"Finansal \\xF6zg\\xFCrl\\xFC\\u011F\\xFCn\\xFCze giden yolculuk sadece birka\\xE7 t\\u0131k uza\\u011F\\u0131n\\u0131zda.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://app.butce360.com/register\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"bg-white text-emerald-600 px-8 py-4 rounded-2xl font-bold hover:bg-gray-100 transition-colors shadow-lg\",\n            children: \"\\xDCcretsiz Hesap Olu\\u015Ftur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/iletisim\",\n            className: \"border-2 border-white text-white px-8 py-4 rounded-2xl font-bold hover:bg-white hover:text-emerald-600 transition-colors\",\n            children: \"Daha Fazla Bilgi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = HowItWorksPage;\nexport default HowItWorksPage;\nvar _c;\n$RefreshReg$(_c, \"HowItWorksPage\");", "map": {"version": 3, "names": ["React", "HowItWorks", "jsxDEV", "_jsxDEV", "HowItWorksPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "viewBox", "fillRule", "d", "clipRule", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/nocytech/butce360/presentation_page/src/pages/HowItWorksPage.js"], "sourcesContent": ["import React from 'react';\nimport HowItWorks from '../components/HowItWorks';\n\nconst HowItWorksPage = () => {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Section */}\n      <section className=\"py-20 bg-gradient-to-br from-emerald-50 to-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h1 className=\"text-4xl lg:text-6xl font-bold text-gray-900 mb-6\">\n              Butce360\n              <span className=\"text-emerald-600 block\">Nasıl Çalışır?</span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 leading-relaxed\">\n              Sadece 4 basit adımda finansal takibi başlatın. \n              Teknik bilgi gerektirmez, dakikalar içinde kullanmaya başlayın.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* How It Works Component */}\n      <HowItWorks />\n\n      {/* Detailed Steps */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 mb-6\">\n                Detaylı Adımlar\n              </h2>\n              <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n                Her adımda size rehberlik ediyoruz. Kolay kurulum, hızlı başlangıç.\n              </p>\n            </div>\n\n            <div className=\"space-y-16\">\n              {/* Step 1 */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n                <div>\n                  <div className=\"flex items-center mb-6\">\n                    <div className=\"w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4\">\n                      1\n                    </div>\n                    <h3 className=\"text-2xl font-bold text-gray-900\">Hesap Oluşturun</h3>\n                  </div>\n                  <p className=\"text-lg text-gray-600 mb-6\">\n                    Email adresiniz ile hızlı kayıt olun. Sadece birkaç dakika sürer ve \n                    kredi kartı bilgisi gerektirmez.\n                  </p>\n                  <ul className=\"space-y-3\">\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-700\">Email doğrulama</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-700\">Güvenli şifre oluşturma</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-700\">Profil bilgileri</span>\n                    </li>\n                  </ul>\n                </div>\n                <div className=\"bg-gradient-to-br from-emerald-100 to-blue-100 rounded-3xl p-8 text-center\">\n                  <div className=\"text-6xl mb-4\">👤</div>\n                  <h4 className=\"text-xl font-bold text-gray-900 mb-2\">Hızlı Kayıt</h4>\n                  <p className=\"text-gray-600\">2 dakikada hesabınız hazır</p>\n                </div>\n              </div>\n\n              {/* Step 2 */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n                <div className=\"order-2 lg:order-1\">\n                  <div className=\"bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl p-8 text-center\">\n                    <div className=\"text-6xl mb-4\">🏦</div>\n                    <h4 className=\"text-xl font-bold text-gray-900 mb-2\">Güvenli Bağlantı</h4>\n                    <p className=\"text-gray-600\">Banka seviyesinde güvenlik</p>\n                  </div>\n                </div>\n                <div className=\"order-1 lg:order-2\">\n                  <div className=\"flex items-center mb-6\">\n                    <div className=\"w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4\">\n                      2\n                    </div>\n                    <h3 className=\"text-2xl font-bold text-gray-900\">Hesaplarınızı Ekleyin</h3>\n                  </div>\n                  <p className=\"text-lg text-gray-600 mb-6\">\n                    Banka hesaplarınızı, kredi kartlarınızı ve nakit hesaplarınızı sisteme ekleyin. \n                    Tüm finansal durumunuzu tek yerden görün.\n                  </p>\n                  <ul className=\"space-y-3\">\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-700\">Banka hesapları</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-700\">Kredi kartları</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-700\">Nakit hesaplar</span>\n                    </li>\n                  </ul>\n                </div>\n              </div>\n\n              {/* Step 3 */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n                <div>\n                  <div className=\"flex items-center mb-6\">\n                    <div className=\"w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4\">\n                      3\n                    </div>\n                    <h3 className=\"text-2xl font-bold text-gray-900\">İşlemlerinizi Kaydedin</h3>\n                  </div>\n                  <p className=\"text-lg text-gray-600 mb-6\">\n                    Gelir ve giderlerinizi manuel olarak girin veya banka ekstresini yükleyin. \n                    Sistem otomatik olarak kategorilere ayırır.\n                  </p>\n                  <ul className=\"space-y-3\">\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-700\">Manuel işlem girişi</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-700\">PDF ekstres yükleme</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-700\">Otomatik kategorilendirme</span>\n                    </li>\n                  </ul>\n                </div>\n                <div className=\"bg-gradient-to-br from-purple-100 to-pink-100 rounded-3xl p-8 text-center\">\n                  <div className=\"text-6xl mb-4\">📝</div>\n                  <h4 className=\"text-xl font-bold text-gray-900 mb-2\">Kolay Giriş</h4>\n                  <p className=\"text-gray-600\">Otomatik kategorilendirme</p>\n                </div>\n              </div>\n\n              {/* Step 4 */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n                <div className=\"order-2 lg:order-1\">\n                  <div className=\"bg-gradient-to-br from-orange-100 to-red-100 rounded-3xl p-8 text-center\">\n                    <div className=\"text-6xl mb-4\">📊</div>\n                    <h4 className=\"text-xl font-bold text-gray-900 mb-2\">Akıllı Analiz</h4>\n                    <p className=\"text-gray-600\">Detaylı raporlar ve öneriler</p>\n                  </div>\n                </div>\n                <div className=\"order-1 lg:order-2\">\n                  <div className=\"flex items-center mb-6\">\n                    <div className=\"w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4\">\n                      4\n                    </div>\n                    <h3 className=\"text-2xl font-bold text-gray-900\">Analiz Edin & Planlayın</h3>\n                  </div>\n                  <p className=\"text-lg text-gray-600 mb-6\">\n                    Detaylı raporları inceleyin, harcama alışkanlıklarınızı analiz edin ve \n                    gelecek için akıllı bütçe planları oluşturun.\n                  </p>\n                  <ul className=\"space-y-3\">\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-700\">Detaylı raporlar</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-700\">Bütçe planlama</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-emerald-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-700\">Finansal öneriler</span>\n                    </li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-gradient-to-br from-emerald-600 to-blue-600 text-white\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold mb-6\">\n            Hemen Başlayın!\n          </h2>\n          <p className=\"text-xl mb-8 max-w-2xl mx-auto opacity-90\">\n            Finansal özgürlüğünüze giden yolculuk sadece birkaç tık uzağınızda.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a\n              href=\"https://app.butce360.com/register\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"bg-white text-emerald-600 px-8 py-4 rounded-2xl font-bold hover:bg-gray-100 transition-colors shadow-lg\"\n            >\n              Ücretsiz Hesap Oluştur\n            </a>\n            <a\n              href=\"/iletisim\"\n              className=\"border-2 border-white text-white px-8 py-4 rounded-2xl font-bold hover:bg-white hover:text-emerald-600 transition-colors\"\n            >\n              Daha Fazla Bilgi\n            </a>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default HowItWorksPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,oBACED,OAAA;IAAKE,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpCH,OAAA;MAASE,SAAS,EAAC,kDAAkD;MAAAC,QAAA,eACnEH,OAAA;QAAKE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCH,OAAA;UAAKE,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CH,OAAA;YAAIE,SAAS,EAAC,mDAAmD;YAAAC,QAAA,GAAC,UAEhE,eAAAH,OAAA;cAAME,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACLP,OAAA;YAAGE,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAG1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA,CAACF,UAAU;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGdP,OAAA;MAASE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCH,OAAA;QAAKE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCH,OAAA;UAAKE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCH,OAAA;YAAKE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCH,OAAA;cAAIE,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLP,OAAA;cAAGE,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAEvD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENP,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAEzBH,OAAA;cAAKE,SAAS,EAAC,qDAAqD;cAAAC,QAAA,gBAClEH,OAAA;gBAAAG,QAAA,gBACEH,OAAA;kBAAKE,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCH,OAAA;oBAAKE,SAAS,EAAC,0GAA0G;oBAAAC,QAAA,EAAC;kBAE1H;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNP,OAAA;oBAAIE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACNP,OAAA;kBAAGE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAG1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJP,OAAA;kBAAIE,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACvBH,OAAA;oBAAIE,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/BH,OAAA;sBAAKE,SAAS,EAAC,+BAA+B;sBAACM,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAN,QAAA,eACpFH,OAAA;wBAAMU,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,eACNP,OAAA;sBAAME,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACLP,OAAA;oBAAIE,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/BH,OAAA;sBAAKE,SAAS,EAAC,+BAA+B;sBAACM,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAN,QAAA,eACpFH,OAAA;wBAAMU,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,eACNP,OAAA;sBAAME,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,eACLP,OAAA;oBAAIE,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/BH,OAAA;sBAAKE,SAAS,EAAC,+BAA+B;sBAACM,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAN,QAAA,eACpFH,OAAA;wBAAMU,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,eACNP,OAAA;sBAAME,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNP,OAAA;gBAAKE,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,gBACzFH,OAAA;kBAAKE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvCP,OAAA;kBAAIE,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrEP,OAAA;kBAAGE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNP,OAAA;cAAKE,SAAS,EAAC,qDAAqD;cAAAC,QAAA,gBAClEH,OAAA;gBAAKE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eACjCH,OAAA;kBAAKE,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,gBACxFH,OAAA;oBAAKE,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvCP,OAAA;oBAAIE,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1EP,OAAA;oBAAGE,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNP,OAAA;gBAAKE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCH,OAAA;kBAAKE,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCH,OAAA;oBAAKE,SAAS,EAAC,0GAA0G;oBAAAC,QAAA,EAAC;kBAE1H;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNP,OAAA;oBAAIE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC,eACNP,OAAA;kBAAGE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAG1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJP,OAAA;kBAAIE,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACvBH,OAAA;oBAAIE,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/BH,OAAA;sBAAKE,SAAS,EAAC,+BAA+B;sBAACM,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAN,QAAA,eACpFH,OAAA;wBAAMU,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,eACNP,OAAA;sBAAME,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACLP,OAAA;oBAAIE,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/BH,OAAA;sBAAKE,SAAS,EAAC,+BAA+B;sBAACM,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAN,QAAA,eACpFH,OAAA;wBAAMU,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,eACNP,OAAA;sBAAME,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACLP,OAAA;oBAAIE,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/BH,OAAA;sBAAKE,SAAS,EAAC,+BAA+B;sBAACM,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAN,QAAA,eACpFH,OAAA;wBAAMU,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,eACNP,OAAA;sBAAME,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNP,OAAA;cAAKE,SAAS,EAAC,qDAAqD;cAAAC,QAAA,gBAClEH,OAAA;gBAAAG,QAAA,gBACEH,OAAA;kBAAKE,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCH,OAAA;oBAAKE,SAAS,EAAC,0GAA0G;oBAAAC,QAAA,EAAC;kBAE1H;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNP,OAAA;oBAAIE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC,eACNP,OAAA;kBAAGE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAG1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJP,OAAA;kBAAIE,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACvBH,OAAA;oBAAIE,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/BH,OAAA;sBAAKE,SAAS,EAAC,+BAA+B;sBAACM,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAN,QAAA,eACpFH,OAAA;wBAAMU,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,eACNP,OAAA;sBAAME,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACLP,OAAA;oBAAIE,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/BH,OAAA;sBAAKE,SAAS,EAAC,+BAA+B;sBAACM,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAN,QAAA,eACpFH,OAAA;wBAAMU,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,eACNP,OAAA;sBAAME,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACLP,OAAA;oBAAIE,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/BH,OAAA;sBAAKE,SAAS,EAAC,+BAA+B;sBAACM,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAN,QAAA,eACpFH,OAAA;wBAAMU,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,eACNP,OAAA;sBAAME,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAyB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNP,OAAA;gBAAKE,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,gBACxFH,OAAA;kBAAKE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvCP,OAAA;kBAAIE,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrEP,OAAA;kBAAGE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNP,OAAA;cAAKE,SAAS,EAAC,qDAAqD;cAAAC,QAAA,gBAClEH,OAAA;gBAAKE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eACjCH,OAAA;kBAAKE,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvFH,OAAA;oBAAKE,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvCP,OAAA;oBAAIE,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvEP,OAAA;oBAAGE,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNP,OAAA;gBAAKE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCH,OAAA;kBAAKE,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCH,OAAA;oBAAKE,SAAS,EAAC,0GAA0G;oBAAAC,QAAA,EAAC;kBAE1H;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNP,OAAA;oBAAIE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACNP,OAAA;kBAAGE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAG1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJP,OAAA;kBAAIE,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACvBH,OAAA;oBAAIE,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/BH,OAAA;sBAAKE,SAAS,EAAC,+BAA+B;sBAACM,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAN,QAAA,eACpFH,OAAA;wBAAMU,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,eACNP,OAAA;sBAAME,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC,eACLP,OAAA;oBAAIE,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/BH,OAAA;sBAAKE,SAAS,EAAC,+BAA+B;sBAACM,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAN,QAAA,eACpFH,OAAA;wBAAMU,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,eACNP,OAAA;sBAAME,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACLP,OAAA;oBAAIE,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/BH,OAAA;sBAAKE,SAAS,EAAC,+BAA+B;sBAACM,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAN,QAAA,eACpFH,OAAA;wBAAMU,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,eACNP,OAAA;sBAAME,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAASE,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAClFH,OAAA;QAAKE,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDH,OAAA;UAAIE,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLP,OAAA;UAAGE,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA;UAAKE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DH,OAAA;YACEa,IAAI,EAAC,mCAAmC;YACxCC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBb,SAAS,EAAC,yGAAyG;YAAAC,QAAA,EACpH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJP,OAAA;YACEa,IAAI,EAAC,WAAW;YAChBX,SAAS,EAAC,0HAA0H;YAAAC,QAAA,EACrI;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACS,EAAA,GA7OIf,cAAc;AA+OpB,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}