{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/nocytech/butce360/presentation_page/src/pages/FAQPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FAQPage = () => {\n  _s();\n  const [openFAQ, setOpenFAQ] = useState(null);\n  const faqs = [{\n    category: \"Genel Sorular\",\n    questions: [{\n      question: \"Butce360 nedir ve nasıl çalışır?\",\n      answer: \"Butce360, gelir ve giderlerinizi takip etmenizi, bütçe planlaması yapmanızı ve finansal hedeflerinize ulaşmanızı sağlayan akıllı bir finansal yönetim platformudur. Banka ekstrelerinizi otomatik olarak analiz eder ve size kişiselleştirilmiş öneriler sunar.\"\n    }, {\n      question: \"Verilerim güvende mi?\",\n      answer: \"Eve<PERSON>, verileriniz banka seviyesinde güvenlik ile korunur. SSL şifreleme kullanıyoruz ve KVKK uyumlu veri işleme yapıyoruz. Finansal verileriniz hiçbir zaman üçüncü taraflarla paylaşılmaz.\"\n    }, {\n      question: \"Hangi bankaları destekliyorsunuz?\",\n      answer: \"Şu anda Türkiye'deki tüm büyük bankaların PDF ekstrelerini destekliyoruz. VakıfBank, QNB, Garanti BBVA, İş Bankası, Akbank ve diğer bankaların ekstrelerini yükleyebilirsiniz.\"\n    }, {\n      question: \"Mobil uygulamanız var mı?\",\n      answer: \"Evet, iOS ve Android için mobil uygulamamız mevcuttur. Web platformumuz da mobil uyumlu olarak tasarlanmıştır, böylece her cihazdan erişebilirsiniz.\"\n    }]\n  }, {\n    category: \"Özellikler\",\n    questions: [{\n      question: \"Otomatik kategorilendirme nasıl çalışır?\",\n      answer: \"Yapay zeka algoritmalarımız, işlem açıklamalarını analiz ederek otomatik olarak uygun kategorilere atar. Sistem zamanla öğrenir ve daha doğru kategorilendirme yapar. İsterseniz manuel olarak da düzenleyebilirsiniz.\"\n    }, {\n      question: \"Bütçe planlama özelliği nasıl kullanılır?\",\n      answer: \"Her kategori için aylık bütçe limitleri belirleyebilirsiniz. Sistem, harcamalarınızı takip eder ve limite yaklaştığınızda sizi uyarır. Ayrıca geçmiş verilerinize dayanarak akıllı bütçe önerileri sunar.\"\n    }, {\n      question: \"Hangi tür raporlar alabilirim?\",\n      answer: \"Aylık/yıllık harcama raporları, kategori bazlı analizler, gelir-gider karşılaştırmaları, trend analizleri ve özel dönem raporları alabilirsiniz. Tüm raporları PDF olarak indirebilirsiniz.\"\n    }, {\n      question: \"Çoklu hesap yönetimi nasıl çalışır?\",\n      answer: \"Birden fazla banka hesabı, kredi kartı ve nakit hesabınızı tek platformda yönetebilirsiniz. Her hesap için ayrı bakiye takibi yapılır ve toplam finansal durumunuzu görebilirsiniz.\"\n    }]\n  }, {\n    category: \"Fiyatlandırma\",\n    questions: [{\n      question: \"Ücretsiz plan sınırları nelerdir?\",\n      answer: \"Ücretsiz planda 3 hesap bağlayabilir, ayda 100 işlem kaydedebilir ve temel raporlara erişebilirsiniz. Mobil uygulama ve email desteği dahildir.\"\n    }, {\n      question: \"Ödeme yöntemleri nelerdir?\",\n      answer: \"Kredi kartı, banka kartı ve havale ile ödeme yapabilirsiniz. Tüm ödemeler güvenli SSL bağlantısı üzerinden işlenir.\"\n    }, {\n      question: \"İptal etmek istersem ne yapmalıyım?\",\n      answer: \"İstediğiniz zaman hesap ayarlarından aboneliğinizi iptal edebilirsiniz. İptal sonrası mevcut dönem sonuna kadar hizmet almaya devam edersiniz.\"\n    }, {\n      question: \"Para iade politikanız nedir?\",\n      answer: \"Tüm paketlerde 30 gün para iade garantisi sunuyoruz. Memnun kalmazsanız, tam ücret iadesi alabilirsiniz.\"\n    }]\n  }, {\n    category: \"Teknik Destek\",\n    questions: [{\n      question: \"Teknik sorun yaşarsam ne yapmalıyım?\",\n      answer: \"Teknik sorunlar için <EMAIL> adresine email gönderebilir veya WhatsApp üzerinden bizimle iletişime geçebilirsiniz. Genellikle 24 saat içinde yanıt veriyoruz.\"\n    }, {\n      question: \"Veri yedekleme yapıyor musunuz?\",\n      answer: \"Evet, verileriniz günlük olarak yedeklenir ve birden fazla sunucuda saklanır. Veri kaybı riski minimumdur.\"\n    }, {\n      question: \"API erişimi var mı?\",\n      answer: \"Profesyonel ve Kurumsal paketlerde API erişimi mevcuttur. Kendi uygulamalarınızla entegrasyon yapabilirsiniz.\"\n    }, {\n      question: \"Sistem güncellemeleri nasıl yapılır?\",\n      answer: \"Tüm güncellemeler otomatik olarak yapılır. Yeni özellikler ve iyileştirmeler hakkında email ile bilgilendirilirsiniz.\"\n    }]\n  }];\n  const toggleFAQ = (categoryIndex, questionIndex) => {\n    const faqKey = `${categoryIndex}-${questionIndex}`;\n    setOpenFAQ(openFAQ === faqKey ? null : faqKey);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gradient-to-br from-emerald-50 to-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl lg:text-6xl font-bold text-gray-900 mb-6\",\n            children: [\"S\\u0131k\\xE7a Sorulan\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-emerald-600 block\",\n              children: \"Sorular\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n            children: \"Butce360 hakk\\u0131nda merak ettiklerinizin yan\\u0131tlar\\u0131n\\u0131 burada bulabilirsiniz. Arad\\u0131\\u011F\\u0131n\\u0131z\\u0131 bulamazsan\\u0131z bizimle ileti\\u015Fime ge\\xE7in.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://wa.me/905417173986\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"bg-green-600 text-white px-8 py-4 rounded-2xl font-bold hover:bg-green-700 transition-colors shadow-lg\",\n              children: \"WhatsApp ile Sor\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/iletisim\",\n              className: \"border-2 border-emerald-600 text-emerald-600 px-8 py-4 rounded-2xl font-bold hover:bg-emerald-600 hover:text-white transition-colors\",\n              children: \"\\u0130leti\\u015Fim Formu\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto\",\n          children: faqs.map((category, categoryIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-8 pb-4 border-b-2 border-emerald-100\",\n              children: category.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: category.questions.map((faq, questionIndex) => {\n                const faqKey = `${categoryIndex}-${questionIndex}`;\n                const isOpen = openFAQ === faqKey;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-2xl overflow-hidden border border-gray-100 hover:border-emerald-200 transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleFAQ(categoryIndex, questionIndex),\n                    className: \"w-full px-6 py-6 text-left flex justify-between items-center hover:bg-gray-100 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg font-semibold text-gray-900 pr-4\",\n                      children: faq.question\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 155,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: `w-6 h-6 text-emerald-600 transform transition-transform ${isOpen ? 'rotate-180' : ''}`,\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M19 9l-7 7-7-7\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 164,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 158,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 25\n                  }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"px-6 pb-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 leading-relaxed\",\n                      children: faq.answer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 27\n                  }, this)]\n                }, questionIndex, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this)]\n          }, categoryIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gradient-to-br from-emerald-600 to-blue-600 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl lg:text-4xl font-bold mb-6\",\n          children: \"Hala Sorunuz mu Var?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl mb-8 max-w-2xl mx-auto opacity-90\",\n          children: \"Uzman ekibimiz size yard\\u0131mc\\u0131 olmak i\\xE7in burada. Hemen ileti\\u015Fime ge\\xE7in ve t\\xFCm sorular\\u0131n\\u0131z\\u0131n yan\\u0131t\\u0131n\\u0131 al\\u0131n.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/iletisim\",\n            className: \"bg-white text-emerald-600 px-8 py-4 rounded-2xl font-bold hover:bg-gray-100 transition-colors shadow-lg\",\n            children: \"\\u0130leti\\u015Fim Formu\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://wa.me/905417173986\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"border-2 border-white text-white px-8 py-4 rounded-2xl font-bold hover:bg-white hover:text-emerald-600 transition-colors\",\n            children: \"WhatsApp Destek\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-6xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-3xl font-bold text-gray-900 mb-6\",\n              children: \"H\\u0131zl\\u0131 Eri\\u015Fim\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg text-gray-600\",\n              children: \"S\\u0131k kullan\\u0131lan sayfalara h\\u0131zl\\u0131 eri\\u015Fim\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/nedir\",\n              className: \"bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 hover:shadow-lg transition-shadow group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-4\",\n                children: \"\\u2753\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors\",\n                children: \"Butce360 Nedir?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/nasil-calisir\",\n              className: \"bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 hover:shadow-lg transition-shadow group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-4\",\n                children: \"\\u2699\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors\",\n                children: \"Nas\\u0131l \\xC7al\\u0131\\u015F\\u0131r?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/ucretlendirme\",\n              className: \"bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 hover:shadow-lg transition-shadow group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-4\",\n                children: \"\\uD83D\\uDCB0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors\",\n                children: \"Fiyatland\\u0131rma\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://app.butce360.com/register\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 hover:shadow-lg transition-shadow group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-4\",\n                children: \"\\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors\",\n                children: \"Hemen Ba\\u015Fla\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(FAQPage, \"d+ViTGxsUZSITU7uQz5WZ9Fcob0=\");\n_c = FAQPage;\nexport default FAQPage;\nvar _c;\n$RefreshReg$(_c, \"FAQPage\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "FAQPage", "_s", "openFAQ", "setOpenFAQ", "faqs", "category", "questions", "question", "answer", "toggleFAQ", "categoryIndex", "questionIndex", "faqKey", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "target", "rel", "map", "faq", "isOpen", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/nocytech/butce360/presentation_page/src/pages/FAQPage.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst FAQPage = () => {\n  const [openFAQ, setOpenFAQ] = useState(null);\n\n  const faqs = [\n    {\n      category: \"Genel Sorular\",\n      questions: [\n        {\n          question: \"Butce360 nedir ve nasıl çalışır?\",\n          answer: \"Butce360, gelir ve giderlerinizi takip etmenizi, bütçe planlaması yapmanızı ve finansal hedeflerinize ulaşmanızı sağlayan akıllı bir finansal yönetim platformudur. Banka ekstrelerinizi otomatik olarak analiz eder ve size kişiselleştirilmiş öneriler sunar.\"\n        },\n        {\n          question: \"Verilerim güvende mi?\",\n          answer: \"Evet, verileriniz banka seviyesinde güvenlik ile korunur. SSL şifreleme kullanıyoruz ve KVKK uyumlu veri işleme yapıyoruz. Finansal verileriniz hiçbir zaman üçüncü taraflarla paylaşılmaz.\"\n        },\n        {\n          question: \"Hangi bankaları destekliyorsunuz?\",\n          answer: \"Şu anda Türkiye'deki tüm büyük bankaların PDF ekstrelerini destekliyoruz. VakıfBank, QNB, Garanti BBVA, İş Bankası, Akbank ve diğer bankaların ekstrelerini yükleyebilirsiniz.\"\n        },\n        {\n          question: \"Mobil uygulamanız var mı?\",\n          answer: \"Evet, iOS ve Android için mobil uygulamamız mevcuttur. Web platformumuz da mobil uyumlu olarak tasarlanmıştır, böylece her cihazdan erişebilirsiniz.\"\n        }\n      ]\n    },\n    {\n      category: \"Özellikler\",\n      questions: [\n        {\n          question: \"Otomatik kategorilendirme nasıl çalışır?\",\n          answer: \"Yapay zeka algoritmalarımız, işlem açıklamalarını analiz ederek otomatik olarak uygun kategorilere atar. Sistem zamanla öğrenir ve daha doğru kategorilendirme yapar. İsterseniz manuel olarak da düzenleyebilirsiniz.\"\n        },\n        {\n          question: \"Bütçe planlama özelliği nasıl kullanılır?\",\n          answer: \"Her kategori için aylık bütçe limitleri belirleyebilirsiniz. Sistem, harcamalarınızı takip eder ve limite yaklaştığınızda sizi uyarır. Ayrıca geçmiş verilerinize dayanarak akıllı bütçe önerileri sunar.\"\n        },\n        {\n          question: \"Hangi tür raporlar alabilirim?\",\n          answer: \"Aylık/yıllık harcama raporları, kategori bazlı analizler, gelir-gider karşılaştırmaları, trend analizleri ve özel dönem raporları alabilirsiniz. Tüm raporları PDF olarak indirebilirsiniz.\"\n        },\n        {\n          question: \"Çoklu hesap yönetimi nasıl çalışır?\",\n          answer: \"Birden fazla banka hesabı, kredi kartı ve nakit hesabınızı tek platformda yönetebilirsiniz. Her hesap için ayrı bakiye takibi yapılır ve toplam finansal durumunuzu görebilirsiniz.\"\n        }\n      ]\n    },\n    {\n      category: \"Fiyatlandırma\",\n      questions: [\n        {\n          question: \"Ücretsiz plan sınırları nelerdir?\",\n          answer: \"Ücretsiz planda 3 hesap bağlayabilir, ayda 100 işlem kaydedebilir ve temel raporlara erişebilirsiniz. Mobil uygulama ve email desteği dahildir.\"\n        },\n        {\n          question: \"Ödeme yöntemleri nelerdir?\",\n          answer: \"Kredi kartı, banka kartı ve havale ile ödeme yapabilirsiniz. Tüm ödemeler güvenli SSL bağlantısı üzerinden işlenir.\"\n        },\n        {\n          question: \"İptal etmek istersem ne yapmalıyım?\",\n          answer: \"İstediğiniz zaman hesap ayarlarından aboneliğinizi iptal edebilirsiniz. İptal sonrası mevcut dönem sonuna kadar hizmet almaya devam edersiniz.\"\n        },\n        {\n          question: \"Para iade politikanız nedir?\",\n          answer: \"Tüm paketlerde 30 gün para iade garantisi sunuyoruz. Memnun kalmazsanız, tam ücret iadesi alabilirsiniz.\"\n        }\n      ]\n    },\n    {\n      category: \"Teknik Destek\",\n      questions: [\n        {\n          question: \"Teknik sorun yaşarsam ne yapmalıyım?\",\n          answer: \"Teknik sorunlar için <EMAIL> adresine email gönderebilir veya WhatsApp üzerinden bizimle iletişime geçebilirsiniz. Genellikle 24 saat içinde yanıt veriyoruz.\"\n        },\n        {\n          question: \"Veri yedekleme yapıyor musunuz?\",\n          answer: \"Evet, verileriniz günlük olarak yedeklenir ve birden fazla sunucuda saklanır. Veri kaybı riski minimumdur.\"\n        },\n        {\n          question: \"API erişimi var mı?\",\n          answer: \"Profesyonel ve Kurumsal paketlerde API erişimi mevcuttur. Kendi uygulamalarınızla entegrasyon yapabilirsiniz.\"\n        },\n        {\n          question: \"Sistem güncellemeleri nasıl yapılır?\",\n          answer: \"Tüm güncellemeler otomatik olarak yapılır. Yeni özellikler ve iyileştirmeler hakkında email ile bilgilendirilirsiniz.\"\n        }\n      ]\n    }\n  ];\n\n  const toggleFAQ = (categoryIndex, questionIndex) => {\n    const faqKey = `${categoryIndex}-${questionIndex}`;\n    setOpenFAQ(openFAQ === faqKey ? null : faqKey);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Section */}\n      <section className=\"py-20 bg-gradient-to-br from-emerald-50 to-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h1 className=\"text-4xl lg:text-6xl font-bold text-gray-900 mb-6\">\n              Sıkça Sorulan\n              <span className=\"text-emerald-600 block\">Sorular</span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 leading-relaxed\">\n              Butce360 hakkında merak ettiklerinizin yanıtlarını burada bulabilirsiniz. \n              Aradığınızı bulamazsanız bizimle iletişime geçin.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a \n                href=\"https://wa.me/905417173986\" \n                target=\"_blank\" \n                rel=\"noopener noreferrer\"\n                className=\"bg-green-600 text-white px-8 py-4 rounded-2xl font-bold hover:bg-green-700 transition-colors shadow-lg\"\n              >\n                WhatsApp ile Sor\n              </a>\n              <a \n                href=\"/iletisim\" \n                className=\"border-2 border-emerald-600 text-emerald-600 px-8 py-4 rounded-2xl font-bold hover:bg-emerald-600 hover:text-white transition-colors\"\n              >\n                İletişim Formu\n              </a>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* FAQ Content */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            {faqs.map((category, categoryIndex) => (\n              <div key={categoryIndex} className=\"mb-12\">\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-8 pb-4 border-b-2 border-emerald-100\">\n                  {category.category}\n                </h2>\n                <div className=\"space-y-4\">\n                  {category.questions.map((faq, questionIndex) => {\n                    const faqKey = `${categoryIndex}-${questionIndex}`;\n                    const isOpen = openFAQ === faqKey;\n                    \n                    return (\n                      <div \n                        key={questionIndex}\n                        className=\"bg-gray-50 rounded-2xl overflow-hidden border border-gray-100 hover:border-emerald-200 transition-colors\"\n                      >\n                        <button\n                          onClick={() => toggleFAQ(categoryIndex, questionIndex)}\n                          className=\"w-full px-6 py-6 text-left flex justify-between items-center hover:bg-gray-100 transition-colors\"\n                        >\n                          <span className=\"text-lg font-semibold text-gray-900 pr-4\">\n                            {faq.question}\n                          </span>\n                          <svg \n                            className={`w-6 h-6 text-emerald-600 transform transition-transform ${isOpen ? 'rotate-180' : ''}`}\n                            fill=\"none\" \n                            stroke=\"currentColor\" \n                            viewBox=\"0 0 24 24\"\n                          >\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                          </svg>\n                        </button>\n                        {isOpen && (\n                          <div className=\"px-6 pb-6\">\n                            <p className=\"text-gray-600 leading-relaxed\">\n                              {faq.answer}\n                            </p>\n                          </div>\n                        )}\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Still Have Questions */}\n      <section className=\"py-20 bg-gradient-to-br from-emerald-600 to-blue-600 text-white\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold mb-6\">\n            Hala Sorunuz mu Var?\n          </h2>\n          <p className=\"text-xl mb-8 max-w-2xl mx-auto opacity-90\">\n            Uzman ekibimiz size yardımcı olmak için burada. \n            Hemen iletişime geçin ve tüm sorularınızın yanıtını alın.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a\n              href=\"/iletisim\"\n              className=\"bg-white text-emerald-600 px-8 py-4 rounded-2xl font-bold hover:bg-gray-100 transition-colors shadow-lg\"\n            >\n              İletişim Formu\n            </a>\n            <a\n              href=\"https://wa.me/905417173986\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"border-2 border-white text-white px-8 py-4 rounded-2xl font-bold hover:bg-white hover:text-emerald-600 transition-colors\"\n            >\n              WhatsApp Destek\n            </a>\n          </div>\n        </div>\n      </section>\n\n      {/* Quick Links */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">\n                Hızlı Erişim\n              </h2>\n              <p className=\"text-lg text-gray-600\">\n                Sık kullanılan sayfalara hızlı erişim\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <a href=\"/nedir\" className=\"bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 hover:shadow-lg transition-shadow group\">\n                <div className=\"text-4xl mb-4\">❓</div>\n                <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors\">\n                  Butce360 Nedir?\n                </h3>\n              </a>\n\n              <a href=\"/nasil-calisir\" className=\"bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 hover:shadow-lg transition-shadow group\">\n                <div className=\"text-4xl mb-4\">⚙️</div>\n                <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors\">\n                  Nasıl Çalışır?\n                </h3>\n              </a>\n\n              <a href=\"/ucretlendirme\" className=\"bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 hover:shadow-lg transition-shadow group\">\n                <div className=\"text-4xl mb-4\">💰</div>\n                <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors\">\n                  Fiyatlandırma\n                </h3>\n              </a>\n\n              <a href=\"https://app.butce360.com/register\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 hover:shadow-lg transition-shadow group\">\n                <div className=\"text-4xl mb-4\">🚀</div>\n                <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors\">\n                  Hemen Başla\n                </h3>\n              </a>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default FAQPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGN,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMO,IAAI,GAAG,CACX;IACEC,QAAQ,EAAE,eAAe;IACzBC,SAAS,EAAE,CACT;MACEC,QAAQ,EAAE,kCAAkC;MAC5CC,MAAM,EAAE;IACV,CAAC,EACD;MACED,QAAQ,EAAE,uBAAuB;MACjCC,MAAM,EAAE;IACV,CAAC,EACD;MACED,QAAQ,EAAE,mCAAmC;MAC7CC,MAAM,EAAE;IACV,CAAC,EACD;MACED,QAAQ,EAAE,2BAA2B;MACrCC,MAAM,EAAE;IACV,CAAC;EAEL,CAAC,EACD;IACEH,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE,CACT;MACEC,QAAQ,EAAE,0CAA0C;MACpDC,MAAM,EAAE;IACV,CAAC,EACD;MACED,QAAQ,EAAE,2CAA2C;MACrDC,MAAM,EAAE;IACV,CAAC,EACD;MACED,QAAQ,EAAE,gCAAgC;MAC1CC,MAAM,EAAE;IACV,CAAC,EACD;MACED,QAAQ,EAAE,qCAAqC;MAC/CC,MAAM,EAAE;IACV,CAAC;EAEL,CAAC,EACD;IACEH,QAAQ,EAAE,eAAe;IACzBC,SAAS,EAAE,CACT;MACEC,QAAQ,EAAE,mCAAmC;MAC7CC,MAAM,EAAE;IACV,CAAC,EACD;MACED,QAAQ,EAAE,4BAA4B;MACtCC,MAAM,EAAE;IACV,CAAC,EACD;MACED,QAAQ,EAAE,qCAAqC;MAC/CC,MAAM,EAAE;IACV,CAAC,EACD;MACED,QAAQ,EAAE,8BAA8B;MACxCC,MAAM,EAAE;IACV,CAAC;EAEL,CAAC,EACD;IACEH,QAAQ,EAAE,eAAe;IACzBC,SAAS,EAAE,CACT;MACEC,QAAQ,EAAE,sCAAsC;MAChDC,MAAM,EAAE;IACV,CAAC,EACD;MACED,QAAQ,EAAE,iCAAiC;MAC3CC,MAAM,EAAE;IACV,CAAC,EACD;MACED,QAAQ,EAAE,qBAAqB;MAC/BC,MAAM,EAAE;IACV,CAAC,EACD;MACED,QAAQ,EAAE,sCAAsC;MAChDC,MAAM,EAAE;IACV,CAAC;EAEL,CAAC,CACF;EAED,MAAMC,SAAS,GAAGA,CAACC,aAAa,EAAEC,aAAa,KAAK;IAClD,MAAMC,MAAM,GAAG,GAAGF,aAAa,IAAIC,aAAa,EAAE;IAClDR,UAAU,CAACD,OAAO,KAAKU,MAAM,GAAG,IAAI,GAAGA,MAAM,CAAC;EAChD,CAAC;EAED,oBACEb,OAAA;IAAKc,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpCf,OAAA;MAASc,SAAS,EAAC,kDAAkD;MAAAC,QAAA,eACnEf,OAAA;QAAKc,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCf,OAAA;UAAKc,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5Cf,OAAA;YAAIc,SAAS,EAAC,mDAAmD;YAAAC,QAAA,GAAC,uBAEhE,eAAAf,OAAA;cAAMc,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACLnB,OAAA;YAAGc,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAG1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJnB,OAAA;YAAKc,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7Df,OAAA;cACEoB,IAAI,EAAC,4BAA4B;cACjCC,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzBR,SAAS,EAAC,wGAAwG;cAAAC,QAAA,EACnH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJnB,OAAA;cACEoB,IAAI,EAAC,WAAW;cAChBN,SAAS,EAAC,sIAAsI;cAAAC,QAAA,EACjJ;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVnB,OAAA;MAASc,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjCf,OAAA;QAAKc,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCf,OAAA;UAAKc,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC/BV,IAAI,CAACkB,GAAG,CAAC,CAACjB,QAAQ,EAAEK,aAAa,kBAChCX,OAAA;YAAyBc,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACxCf,OAAA;cAAIc,SAAS,EAAC,0EAA0E;cAAAC,QAAA,EACrFT,QAAQ,CAACA;YAAQ;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACLnB,OAAA;cAAKc,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBT,QAAQ,CAACC,SAAS,CAACgB,GAAG,CAAC,CAACC,GAAG,EAAEZ,aAAa,KAAK;gBAC9C,MAAMC,MAAM,GAAG,GAAGF,aAAa,IAAIC,aAAa,EAAE;gBAClD,MAAMa,MAAM,GAAGtB,OAAO,KAAKU,MAAM;gBAEjC,oBACEb,OAAA;kBAEEc,SAAS,EAAC,0GAA0G;kBAAAC,QAAA,gBAEpHf,OAAA;oBACE0B,OAAO,EAAEA,CAAA,KAAMhB,SAAS,CAACC,aAAa,EAAEC,aAAa,CAAE;oBACvDE,SAAS,EAAC,kGAAkG;oBAAAC,QAAA,gBAE5Gf,OAAA;sBAAMc,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,EACvDS,GAAG,CAAChB;oBAAQ;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC,eACPnB,OAAA;sBACEc,SAAS,EAAE,2DAA2DW,MAAM,GAAG,YAAY,GAAG,EAAE,EAAG;sBACnGE,IAAI,EAAC,MAAM;sBACXC,MAAM,EAAC,cAAc;sBACrBC,OAAO,EAAC,WAAW;sBAAAd,QAAA,eAEnBf,OAAA;wBAAM8B,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAAgB;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,EACRM,MAAM,iBACLzB,OAAA;oBAAKc,SAAS,EAAC,WAAW;oBAAAC,QAAA,eACxBf,OAAA;sBAAGc,SAAS,EAAC,+BAA+B;sBAAAC,QAAA,EACzCS,GAAG,CAACf;oBAAM;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CACN;gBAAA,GAzBIP,aAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0Bf,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAxCER,aAAa;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyClB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVnB,OAAA;MAASc,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAClFf,OAAA;QAAKc,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDf,OAAA;UAAIc,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnB,OAAA;UAAGc,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EAAC;QAGzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJnB,OAAA;UAAKc,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7Df,OAAA;YACEoB,IAAI,EAAC,WAAW;YAChBN,SAAS,EAAC,yGAAyG;YAAAC,QAAA,EACpH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJnB,OAAA;YACEoB,IAAI,EAAC,4BAA4B;YACjCC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBR,SAAS,EAAC,0HAA0H;YAAAC,QAAA,EACrI;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVnB,OAAA;MAASc,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCf,OAAA;QAAKc,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCf,OAAA;UAAKc,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCf,OAAA;YAAKc,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCf,OAAA;cAAIc,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnB,OAAA;cAAGc,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENnB,OAAA;YAAKc,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnEf,OAAA;cAAGoB,IAAI,EAAC,QAAQ;cAACN,SAAS,EAAC,+GAA+G;cAAAC,QAAA,gBACxIf,OAAA;gBAAKc,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtCnB,OAAA;gBAAIc,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,EAAC;cAEnG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEJnB,OAAA;cAAGoB,IAAI,EAAC,gBAAgB;cAACN,SAAS,EAAC,+GAA+G;cAAAC,QAAA,gBAChJf,OAAA;gBAAKc,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCnB,OAAA;gBAAIc,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,EAAC;cAEnG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEJnB,OAAA;cAAGoB,IAAI,EAAC,gBAAgB;cAACN,SAAS,EAAC,+GAA+G;cAAAC,QAAA,gBAChJf,OAAA;gBAAKc,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCnB,OAAA;gBAAIc,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,EAAC;cAEnG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEJnB,OAAA;cAAGoB,IAAI,EAAC,mCAAmC;cAACC,MAAM,EAAC,QAAQ;cAACC,GAAG,EAAC,qBAAqB;cAACR,SAAS,EAAC,+GAA+G;cAAAC,QAAA,gBAC7Mf,OAAA;gBAAKc,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCnB,OAAA;gBAAIc,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,EAAC;cAEnG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACjB,EAAA,CAjQID,OAAO;AAAAiC,EAAA,GAAPjC,OAAO;AAmQb,eAAeA,OAAO;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}