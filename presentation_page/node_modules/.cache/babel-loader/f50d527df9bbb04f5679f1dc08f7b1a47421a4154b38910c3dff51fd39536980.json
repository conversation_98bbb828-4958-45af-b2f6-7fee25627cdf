{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/nocytech/butce360/presentation_page/src/components/Contact.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Contact = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    company: '',\n    message: ''\n  });\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    // Form submission logic here\n    console.log('Form submitted:', formData);\n    alert('Mesajınız gönderildi! En kısa sürede size dönüş yapacağız.');\n    setFormData({\n      name: '',\n      email: '',\n      company: '',\n      message: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-24 bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-block bg-white rounded-full px-6 py-2 mb-6 shadow-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600 font-semibold text-sm\",\n            children: \"\\u0130LET\\u0130\\u015E\\u0130M\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\",\n          children: \"Bizimle \\u0130leti\\u015Fime Ge\\xE7in\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"Sorular\\u0131n\\u0131z m\\u0131 var? Uzman ekibimiz size yard\\u0131mc\\u0131 olmak i\\xE7in burada. Hemen ileti\\u015Fime ge\\xE7in ve size en uygun \\xE7\\xF6z\\xFCm\\xFC bulal\\u0131m.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 max-w-6xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900 mb-6\",\n              children: \"H\\u0131zl\\u0131 \\u0130leti\\u015Fim\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0 w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-emerald-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 54,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 53,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-semibold text-gray-900\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 58,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"<EMAIL>\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0 w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-emerald-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 66,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 65,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-semibold text-gray-900\",\n                    children: \"Telefon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 70,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"+90 541 717 39 86\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 71,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Pazartesi - Cuma, 09:00 - 18:00\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 72,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0 w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-emerald-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 79,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 80,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 78,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-semibold text-gray-900\",\n                    children: \"Adres\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 84,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"\\u0130stanbul, T\\xFCrkiye\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Uzaktan \\xE7al\\u0131\\u015Fma modeli\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 86,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-6 border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"H\\u0131zl\\u0131 Ba\\u015Flang\\u0131\\xE7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"https://app.butce360.com/register\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"block w-full bg-emerald-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-emerald-700 transition-colors text-center\",\n                children: \"\\xDCcretsiz Hesap Olu\\u015Ftur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"https://wa.me/905417173986\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"block w-full bg-green-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-green-700 transition-colors text-center\",\n                children: \"WhatsApp ile \\u0130leti\\u015Fim\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-3xl p-8 shadow-lg border border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-900 mb-6\",\n            children: \"Mesaj G\\xF6nderin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                children: \"Ad Soyad *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"name\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleChange,\n                required: true,\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors\",\n                placeholder: \"Ad\\u0131n\\u0131z\\u0131 ve soyad\\u0131n\\u0131z\\u0131 girin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                children: \"Email *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                id: \"email\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleChange,\n                required: true,\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors\",\n                placeholder: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"company\",\n                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                children: \"\\u015Eirket\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"company\",\n                name: \"company\",\n                value: formData.company,\n                onChange: handleChange,\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors\",\n                placeholder: \"\\u015Eirket ad\\u0131n\\u0131z (opsiyonel)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"message\",\n                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                children: \"Mesaj *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"message\",\n                name: \"message\",\n                value: formData.message,\n                onChange: handleChange,\n                required: true,\n                rows: 5,\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors resize-none\",\n                placeholder: \"Mesaj\\u0131n\\u0131z\\u0131 buraya yaz\\u0131n...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-8 py-4 rounded-xl font-bold hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-emerald-500/25\",\n              children: \"Mesaj G\\xF6nder\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(Contact, \"RntlhXtfbyo/vpI0Wn7T1dY+8t0=\");\n_c = Contact;\nexport default Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Contact", "_s", "formData", "setFormData", "name", "email", "company", "message", "handleChange", "e", "target", "value", "handleSubmit", "preventDefault", "console", "log", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "href", "rel", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "placeholder", "rows", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/nocytech/butce360/presentation_page/src/components/Contact.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst Contact = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    company: '',\n    message: ''\n  });\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    // Form submission logic here\n    console.log('Form submitted:', formData);\n    alert('Mesajınız gönderildi! En kısa sürede size dönüş yapacağız.');\n    setFormData({ name: '', email: '', company: '', message: '' });\n  };\n\n  return (\n    <section className=\"py-24 bg-gray-50\">\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-block bg-white rounded-full px-6 py-2 mb-6 shadow-sm\">\n            <span className=\"text-gray-600 font-semibold text-sm\">İLETİŞİM</span>\n          </div>\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n            Bizimle İletişime Geçin\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Sorularınız mı var? Uzman ekibimiz size yardımcı olmak için burada. \n            Hemen iletişime geçin ve size en uygun çözümü bulalım.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 max-w-6xl mx-auto\">\n          {/* Contact Info */}\n          <div className=\"space-y-8\">\n            <div>\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">\n                Hızlı İletişim\n              </h3>\n              <div className=\"space-y-6\">\n                <div className=\"flex items-start\">\n                  <div className=\"flex-shrink-0 w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-emerald-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                    </svg>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h4 className=\"text-lg font-semibold text-gray-900\">Email</h4>\n                    <p className=\"text-gray-600\"><EMAIL></p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <div className=\"flex-shrink-0 w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-emerald-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                    </svg>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h4 className=\"text-lg font-semibold text-gray-900\">Telefon</h4>\n                    <p className=\"text-gray-600\">+90 541 717 39 86</p>\n                    <p className=\"text-sm text-gray-500\">Pazartesi - Cuma, 09:00 - 18:00</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <div className=\"flex-shrink-0 w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-emerald-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                    </svg>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h4 className=\"text-lg font-semibold text-gray-900\">Adres</h4>\n                    <p className=\"text-gray-600\">İstanbul, Türkiye</p>\n                    <p className=\"text-sm text-gray-500\">Uzaktan çalışma modeli</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Quick Actions */}\n            <div className=\"bg-white rounded-2xl p-6 border border-gray-100\">\n              <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">Hızlı Başlangıç</h4>\n              <div className=\"space-y-3\">\n                <a\n                  href=\"https://app.butce360.com/register\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"block w-full bg-emerald-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-emerald-700 transition-colors text-center\"\n                >\n                  Ücretsiz Hesap Oluştur\n                </a>\n                <a\n                  href=\"https://wa.me/905417173986\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"block w-full bg-green-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-green-700 transition-colors text-center\"\n                >\n                  WhatsApp ile İletişim\n                </a>\n              </div>\n            </div>\n          </div>\n\n          {/* Contact Form */}\n          <div className=\"bg-white rounded-3xl p-8 shadow-lg border border-gray-100\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">\n              Mesaj Gönderin\n            </h3>\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                  Ad Soyad *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors\"\n                  placeholder=\"Adınızı ve soyadınızı girin\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                  Email *\n                </label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"company\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                  Şirket\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"company\"\n                  name=\"company\"\n                  value={formData.company}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors\"\n                  placeholder=\"Şirket adınız (opsiyonel)\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"message\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                  Mesaj *\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleChange}\n                  required\n                  rows={5}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors resize-none\"\n                  placeholder=\"Mesajınızı buraya yazın...\"\n                />\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"w-full bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-8 py-4 rounded-xl font-bold hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-emerald-500/25\"\n              >\n                Mesaj Gönder\n              </button>\n            </form>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGN,QAAQ,CAAC;IACvCO,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1BN,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACO,CAAC,CAACC,MAAM,CAACN,IAAI,GAAGK,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAIH,CAAC,IAAK;IAC1BA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB;IACAC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEb,QAAQ,CAAC;IACxCc,KAAK,CAAC,4DAA4D,CAAC;IACnEb,WAAW,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAC,CAAC;EAChE,CAAC;EAED,oBACER,OAAA;IAASkB,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eACnCnB,OAAA;MAAKkB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErCnB,OAAA;QAAKkB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCnB,OAAA;UAAKkB,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EnB,OAAA;YAAMkB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACNvB,OAAA;UAAIkB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAElE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLvB,OAAA;UAAGkB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENvB,OAAA;QAAKkB,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBAEvEnB,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBnB,OAAA;YAAAmB,QAAA,gBACEnB,OAAA;cAAIkB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLvB,OAAA;cAAKkB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBnB,OAAA;gBAAKkB,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BnB,OAAA;kBAAKkB,SAAS,EAAC,oFAAoF;kBAAAC,QAAA,eACjGnB,OAAA;oBAAKkB,SAAS,EAAC,0BAA0B;oBAACM,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAP,QAAA,eAC7FnB,OAAA;sBAAM2B,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAsG;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3K;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvB,OAAA;kBAAKkB,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBnB,OAAA;oBAAIkB,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9DvB,OAAA;oBAAGkB,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvB,OAAA;gBAAKkB,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BnB,OAAA;kBAAKkB,SAAS,EAAC,oFAAoF;kBAAAC,QAAA,eACjGnB,OAAA;oBAAKkB,SAAS,EAAC,0BAA0B;oBAACM,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAP,QAAA,eAC7FnB,OAAA;sBAAM2B,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAuN;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5R;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvB,OAAA;kBAAKkB,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBnB,OAAA;oBAAIkB,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChEvB,OAAA;oBAAGkB,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAClDvB,OAAA;oBAAGkB,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvB,OAAA;gBAAKkB,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BnB,OAAA;kBAAKkB,SAAS,EAAC,oFAAoF;kBAAAC,QAAA,eACjGnB,OAAA;oBAAKkB,SAAS,EAAC,0BAA0B;oBAACM,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAP,QAAA,gBAC7FnB,OAAA;sBAAM2B,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoF;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5JvB,OAAA;sBAAM2B,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAkC;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvB,OAAA;kBAAKkB,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBnB,OAAA;oBAAIkB,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9DvB,OAAA;oBAAGkB,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAClDvB,OAAA;oBAAGkB,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvB,OAAA;YAAKkB,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DnB,OAAA;cAAIkB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EvB,OAAA;cAAKkB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBnB,OAAA;gBACE+B,IAAI,EAAC,mCAAmC;gBACxCpB,MAAM,EAAC,QAAQ;gBACfqB,GAAG,EAAC,qBAAqB;gBACzBd,SAAS,EAAC,8HAA8H;gBAAAC,QAAA,EACzI;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJvB,OAAA;gBACE+B,IAAI,EAAC,4BAA4B;gBACjCpB,MAAM,EAAC,QAAQ;gBACfqB,GAAG,EAAC,qBAAqB;gBACzBd,SAAS,EAAC,0HAA0H;gBAAAC,QAAA,EACrI;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvB,OAAA;UAAKkB,SAAS,EAAC,2DAA2D;UAAAC,QAAA,gBACxEnB,OAAA;YAAIkB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLvB,OAAA;YAAMiC,QAAQ,EAAEpB,YAAa;YAACK,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjDnB,OAAA;cAAAmB,QAAA,gBACEnB,OAAA;gBAAOkC,OAAO,EAAC,MAAM;gBAAChB,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAEjF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvB,OAAA;gBACEmC,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,MAAM;gBACT/B,IAAI,EAAC,MAAM;gBACXO,KAAK,EAAET,QAAQ,CAACE,IAAK;gBACrBgC,QAAQ,EAAE5B,YAAa;gBACvB6B,QAAQ;gBACRpB,SAAS,EAAC,mIAAmI;gBAC7IqB,WAAW,EAAC;cAA6B;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvB,OAAA;cAAAmB,QAAA,gBACEnB,OAAA;gBAAOkC,OAAO,EAAC,OAAO;gBAAChB,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAElF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvB,OAAA;gBACEmC,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAC,OAAO;gBACV/B,IAAI,EAAC,OAAO;gBACZO,KAAK,EAAET,QAAQ,CAACG,KAAM;gBACtB+B,QAAQ,EAAE5B,YAAa;gBACvB6B,QAAQ;gBACRpB,SAAS,EAAC,mIAAmI;gBAC7IqB,WAAW,EAAC;cAAmB;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvB,OAAA;cAAAmB,QAAA,gBACEnB,OAAA;gBAAOkC,OAAO,EAAC,SAAS;gBAAChB,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAEpF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvB,OAAA;gBACEmC,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,SAAS;gBACZ/B,IAAI,EAAC,SAAS;gBACdO,KAAK,EAAET,QAAQ,CAACI,OAAQ;gBACxB8B,QAAQ,EAAE5B,YAAa;gBACvBS,SAAS,EAAC,mIAAmI;gBAC7IqB,WAAW,EAAC;cAA2B;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvB,OAAA;cAAAmB,QAAA,gBACEnB,OAAA;gBAAOkC,OAAO,EAAC,SAAS;gBAAChB,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAEpF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvB,OAAA;gBACEoC,EAAE,EAAC,SAAS;gBACZ/B,IAAI,EAAC,SAAS;gBACdO,KAAK,EAAET,QAAQ,CAACK,OAAQ;gBACxB6B,QAAQ,EAAE5B,YAAa;gBACvB6B,QAAQ;gBACRE,IAAI,EAAE,CAAE;gBACRtB,SAAS,EAAC,+IAA+I;gBACzJqB,WAAW,EAAC;cAA4B;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvB,OAAA;cACEmC,IAAI,EAAC,QAAQ;cACbjB,SAAS,EAAC,iNAAiN;cAAAC,QAAA,EAC5N;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACrB,EAAA,CAlMID,OAAO;AAAAwC,EAAA,GAAPxC,OAAO;AAoMb,eAAeA,OAAO;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}