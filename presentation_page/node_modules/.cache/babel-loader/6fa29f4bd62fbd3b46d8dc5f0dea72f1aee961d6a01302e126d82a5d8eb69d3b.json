{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/nocytech/butce360/presentation_page/src/components/Footer.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-gray-900 text-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:col-span-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                className: \"text-3xl font-black bg-gradient-to-r from-emerald-400 to-blue-400 bg-clip-text text-transparent\",\n                children: \"Butce360\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 14,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 13,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 mb-6 leading-relaxed\",\n              children: \"Ak\\u0131ll\\u0131 b\\xFCt\\xE7e y\\xF6netimi ve finansal takip platformu. Paran\\u0131z\\u0131 daha ak\\u0131ll\\u0131ca y\\xF6netin, finansal hedeflerinize ula\\u015F\\u0131n.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"text-gray-400 hover:text-white transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-6 h-6\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 25,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 24,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"text-gray-400 hover:text-white transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-6 h-6\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 30,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 29,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"text-gray-400 hover:text-white transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-6 h-6\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 35,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-bold mb-6\",\n              children: \"\\xDCr\\xFCn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/nedir\",\n                  className: \"text-gray-400 hover:text-white transition-colors\",\n                  children: \"Nedir?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 45,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/nasil-calisir\",\n                  className: \"text-gray-400 hover:text-white transition-colors\",\n                  children: \"Nas\\u0131l \\xC7al\\u0131\\u015F\\u0131r?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 46,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/ucretlendirme\",\n                  className: \"text-gray-400 hover:text-white transition-colors\",\n                  children: \"Fiyatland\\u0131rma\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 47,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://app.butce360.com\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"text-gray-400 hover:text-white transition-colors\",\n                  children: \"Uygulamaya Git\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 48,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-bold mb-6\",\n              children: \"Destek\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/sss\",\n                  className: \"text-gray-400 hover:text-white transition-colors\",\n                  children: \"S\\u0131k\\xE7a Sorulan Sorular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/iletisim\",\n                  className: \"text-gray-400 hover:text-white transition-colors\",\n                  children: \"\\u0130leti\\u015Fim\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"mailto:<EMAIL>\",\n                  className: \"text-gray-400 hover:text-white transition-colors\",\n                  children: \"Email Destek\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 58,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://wa.me/905417173986\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"text-gray-400 hover:text-white transition-colors\",\n                  children: \"WhatsApp Destek\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-bold mb-6\",\n              children: \"\\u015Eirket\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-400 hover:text-white transition-colors\",\n                  children: \"Hakk\\u0131m\\u0131zda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 67,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-400 hover:text-white transition-colors\",\n                  children: \"Blog\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 68,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-400 hover:text-white transition-colors\",\n                  children: \"Kariyer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-400 hover:text-white transition-colors\",\n                  children: \"Bas\\u0131n Kiti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-t border-gray-800 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 md:mb-0\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-sm\",\n              children: \"Butce360 bir NocyTech projesidir.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n              children: \"Gizlilik Politikas\\u0131\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n              children: \"Kullan\\u0131m \\u015Eartlar\\u0131\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n              children: \"KVKK\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 text-gray-400 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm\",\n            children: \"\\xA9 2024 Butce360, T\\xFCm haklar\\u0131 sakl\\u0131d\\u0131r.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "fill", "viewBox", "d", "target", "rel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/nocytech/butce360/presentation_page/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Main Footer */}\n      <div className=\"py-16\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Company Info */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"mb-6\">\n                <Link to=\"/\" className=\"text-3xl font-black bg-gradient-to-r from-emerald-400 to-blue-400 bg-clip-text text-transparent\">\n                  Butce360\n                </Link>\n              </div>\n              <p className=\"text-gray-400 mb-6 leading-relaxed\">\n                Ak<PERSON>ll<PERSON> bütçe yönetimi ve finansal takip platformu. \n                Paranızı daha akıllıca yönetin, finansal hedeflerinize ulaşın.\n              </p>\n              <div className=\"flex space-x-4\">\n                <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                  </svg>\n                </a>\n              </div>\n            </div>\n\n            {/* Product */}\n            <div>\n              <h3 className=\"text-lg font-bold mb-6\">Ürün</h3>\n              <ul className=\"space-y-3\">\n                <li><Link to=\"/nedir\" className=\"text-gray-400 hover:text-white transition-colors\">Nedir?</Link></li>\n                <li><Link to=\"/nasil-calisir\" className=\"text-gray-400 hover:text-white transition-colors\">Nasıl Çalışır?</Link></li>\n                <li><Link to=\"/ucretlendirme\" className=\"text-gray-400 hover:text-white transition-colors\">Fiyatlandırma</Link></li>\n                <li><a href=\"https://app.butce360.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-gray-400 hover:text-white transition-colors\">Uygulamaya Git</a></li>\n              </ul>\n            </div>\n\n            {/* Support */}\n            <div>\n              <h3 className=\"text-lg font-bold mb-6\">Destek</h3>\n              <ul className=\"space-y-3\">\n                <li><Link to=\"/sss\" className=\"text-gray-400 hover:text-white transition-colors\">Sıkça Sorulan Sorular</Link></li>\n                <li><Link to=\"/iletisim\" className=\"text-gray-400 hover:text-white transition-colors\">İletişim</Link></li>\n                <li><a href=\"mailto:<EMAIL>\" className=\"text-gray-400 hover:text-white transition-colors\">Email Destek</a></li>\n                <li><a href=\"https://wa.me/905417173986\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-gray-400 hover:text-white transition-colors\">WhatsApp Destek</a></li>\n              </ul>\n            </div>\n\n            {/* Company */}\n            <div>\n              <h3 className=\"text-lg font-bold mb-6\">Şirket</h3>\n              <ul className=\"space-y-3\">\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">Hakkımızda</a></li>\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">Blog</a></li>\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">Kariyer</a></li>\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">Basın Kiti</a></li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Footer */}\n      <div className=\"border-t border-gray-800 py-8\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"mb-4 md:mb-0\">\n              <p className=\"text-gray-400 text-sm\">\n                Butce360 bir NocyTech projesidir.\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-6\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\n                Gizlilik Politikası\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\n                Kullanım Şartları\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\n                KVKK\n              </a>\n            </div>\n          </div>\n          <div className=\"mt-4 text-gray-400 text-center\">\n            <p className=\"text-sm\">\n              © 2024 Butce360, Tüm hakları saklıdır.\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAQE,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBAExCH,OAAA;MAAKE,SAAS,EAAC,OAAO;MAAAC,QAAA,eACpBH,OAAA;QAAKE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCH,OAAA;UAAKE,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBAEnEH,OAAA;YAAKE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BH,OAAA;cAAKE,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBH,OAAA,CAACF,IAAI;gBAACM,EAAE,EAAC,GAAG;gBAACF,SAAS,EAAC,iGAAiG;gBAAAC,QAAA,EAAC;cAEzH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNR,OAAA;cAAGE,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAGlD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJR,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BH,OAAA;gBAAGS,IAAI,EAAC,GAAG;gBAACP,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,eACtEH,OAAA;kBAAKE,SAAS,EAAC,SAAS;kBAACQ,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAR,QAAA,eAC9DH,OAAA;oBAAMY,CAAC,EAAC;kBAA4f;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACngB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACJR,OAAA;gBAAGS,IAAI,EAAC,GAAG;gBAACP,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,eACtEH,OAAA;kBAAKE,SAAS,EAAC,SAAS;kBAACQ,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAR,QAAA,eAC9DH,OAAA;oBAAMY,CAAC,EAAC;kBAAqe;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5e;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACJR,OAAA;gBAAGS,IAAI,EAAC,GAAG;gBAACP,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,eACtEH,OAAA;kBAAKE,SAAS,EAAC,SAAS;kBAACQ,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAR,QAAA,eAC9DH,OAAA;oBAAMY,CAAC,EAAC;kBAAof;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3f;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNR,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDR,OAAA;cAAIE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvBH,OAAA;gBAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;kBAACM,EAAE,EAAC,QAAQ;kBAACF,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrGR,OAAA;gBAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;kBAACM,EAAE,EAAC,gBAAgB;kBAACF,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrHR,OAAA;gBAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;kBAACM,EAAE,EAAC,gBAAgB;kBAACF,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpHR,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGS,IAAI,EAAC,0BAA0B;kBAACI,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAACZ,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGNR,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClDR,OAAA;cAAIE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvBH,OAAA;gBAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;kBAACM,EAAE,EAAC,MAAM;kBAACF,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClHR,OAAA;gBAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;kBAACM,EAAE,EAAC,WAAW;kBAACF,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1GR,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGS,IAAI,EAAC,2BAA2B;kBAACP,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1HR,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGS,IAAI,EAAC,4BAA4B;kBAACI,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAACZ,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGNR,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClDR,OAAA;cAAIE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvBH,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGS,IAAI,EAAC,GAAG;kBAACP,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChGR,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGS,IAAI,EAAC,GAAG;kBAACP,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1FR,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGS,IAAI,EAAC,GAAG;kBAACP,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7FR,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGS,IAAI,EAAC,GAAG;kBAACP,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNR,OAAA;MAAKE,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CH,OAAA;QAAKE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCH,OAAA;UAAKE,SAAS,EAAC,wDAAwD;UAAAC,QAAA,gBACrEH,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BH,OAAA;cAAGE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNR,OAAA;YAAKE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CH,OAAA;cAAGS,IAAI,EAAC,GAAG;cAACP,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EAAC;YAEjF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJR,OAAA;cAAGS,IAAI,EAAC,GAAG;cAACP,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EAAC;YAEjF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJR,OAAA;cAAGS,IAAI,EAAC,GAAG;cAACP,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EAAC;YAEjF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNR,OAAA;UAAKE,SAAS,EAAC,gCAAgC;UAAAC,QAAA,eAC7CH,OAAA;YAAGE,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAEvB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACO,EAAA,GAvGId,MAAM;AAyGZ,eAAeA,MAAM;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}