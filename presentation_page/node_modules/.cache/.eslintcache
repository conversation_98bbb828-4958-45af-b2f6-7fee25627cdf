[{"/Users/<USER>/nocytech/butce360/presentation_page/src/index.js": "1", "/Users/<USER>/nocytech/butce360/presentation_page/src/App.js": "2", "/Users/<USER>/nocytech/butce360/presentation_page/src/components/Features.js": "3", "/Users/<USER>/nocytech/butce360/presentation_page/src/components/Header.js": "4", "/Users/<USER>/nocytech/butce360/presentation_page/src/components/Footer.js": "5", "/Users/<USER>/nocytech/butce360/presentation_page/src/components/Pricing.js": "6", "/Users/<USER>/nocytech/butce360/presentation_page/src/components/Contact.js": "7", "/Users/<USER>/nocytech/butce360/presentation_page/src/pages/WhatIsPage.js": "8", "/Users/<USER>/nocytech/butce360/presentation_page/src/components/Hero.js": "9", "/Users/<USER>/nocytech/butce360/presentation_page/src/components/HowItWorks.js": "10", "/Users/<USER>/nocytech/butce360/presentation_page/src/pages/FAQPage.js": "11", "/Users/<USER>/nocytech/butce360/presentation_page/src/pages/PricingPage.js": "12", "/Users/<USER>/nocytech/butce360/presentation_page/src/pages/ContactPage.js": "13", "/Users/<USER>/nocytech/butce360/presentation_page/src/pages/HowItWorksPage.js": "14"}, {"size": 254, "mtime": 1755795335636, "results": "15", "hashOfConfig": "16"}, {"size": 3283, "mtime": 1755795335634, "results": "17", "hashOfConfig": "16"}, {"size": 4728, "mtime": 1755795335635, "results": "18", "hashOfConfig": "16"}, {"size": 6532, "mtime": 1755795335635, "results": "19", "hashOfConfig": "16"}, {"size": 6653, "mtime": 1755795335635, "results": "20", "hashOfConfig": "16"}, {"size": 6038, "mtime": 1755795335636, "results": "21", "hashOfConfig": "16"}, {"size": 8944, "mtime": 1755795335634, "results": "22", "hashOfConfig": "16"}, {"size": 8829, "mtime": 1755795335636, "results": "23", "hashOfConfig": "16"}, {"size": 6100, "mtime": 1755795335635, "results": "24", "hashOfConfig": "16"}, {"size": 4233, "mtime": 1755795335635, "results": "25", "hashOfConfig": "16"}, {"size": 12404, "mtime": 1755795335636, "results": "26", "hashOfConfig": "16"}, {"size": 16247, "mtime": 1755795335636, "results": "27", "hashOfConfig": "16"}, {"size": 8938, "mtime": 1755795335636, "results": "28", "hashOfConfig": "16"}, {"size": 14100, "mtime": 1755795335636, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13wc404", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/nocytech/butce360/presentation_page/src/index.js", [], [], "/Users/<USER>/nocytech/butce360/presentation_page/src/App.js", [], [], "/Users/<USER>/nocytech/butce360/presentation_page/src/components/Features.js", [], [], "/Users/<USER>/nocytech/butce360/presentation_page/src/components/Header.js", [], [], "/Users/<USER>/nocytech/butce360/presentation_page/src/components/Footer.js", ["72", "73", "74", "75", "76", "77", "78", "79", "80", "81"], [], "/Users/<USER>/nocytech/butce360/presentation_page/src/components/Pricing.js", [], [], "/Users/<USER>/nocytech/butce360/presentation_page/src/components/Contact.js", [], [], "/Users/<USER>/nocytech/butce360/presentation_page/src/pages/WhatIsPage.js", [], [], "/Users/<USER>/nocytech/butce360/presentation_page/src/components/Hero.js", [], [], "/Users/<USER>/nocytech/butce360/presentation_page/src/components/HowItWorks.js", [], [], "/Users/<USER>/nocytech/butce360/presentation_page/src/pages/FAQPage.js", [], [], "/Users/<USER>/nocytech/butce360/presentation_page/src/pages/PricingPage.js", [], [], "/Users/<USER>/nocytech/butce360/presentation_page/src/pages/ContactPage.js", [], [], "/Users/<USER>/nocytech/butce360/presentation_page/src/pages/HowItWorksPage.js", [], [], {"ruleId": "82", "severity": 1, "message": "83", "line": 23, "column": 17, "nodeType": "84", "endLine": 23, "endColumn": 90}, {"ruleId": "82", "severity": 1, "message": "83", "line": 28, "column": 17, "nodeType": "84", "endLine": 28, "endColumn": 90}, {"ruleId": "82", "severity": 1, "message": "83", "line": 33, "column": 17, "nodeType": "84", "endLine": 33, "endColumn": 90}, {"ruleId": "82", "severity": 1, "message": "83", "line": 67, "column": 21, "nodeType": "84", "endLine": 67, "endColumn": 94}, {"ruleId": "82", "severity": 1, "message": "83", "line": 68, "column": 21, "nodeType": "84", "endLine": 68, "endColumn": 94}, {"ruleId": "82", "severity": 1, "message": "83", "line": 69, "column": 21, "nodeType": "84", "endLine": 69, "endColumn": 94}, {"ruleId": "82", "severity": 1, "message": "83", "line": 70, "column": 21, "nodeType": "84", "endLine": 70, "endColumn": 94}, {"ruleId": "82", "severity": 1, "message": "83", "line": 87, "column": 15, "nodeType": "84", "endLine": 87, "endColumn": 96}, {"ruleId": "82", "severity": 1, "message": "83", "line": 90, "column": 15, "nodeType": "84", "endLine": 90, "endColumn": 96}, {"ruleId": "82", "severity": 1, "message": "83", "line": 93, "column": 15, "nodeType": "84", "endLine": 93, "endColumn": 96}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement"]