# Makefile for Fin Notebook Project

.PHONY: help install dev dev-frontend dev-backend build clean presentation-install presentation-dev presentation-build presentation-docker-build presentation-docker-up presentation-docker-down presentation-docker-logs

# Default target
help:
	@echo "Available commands:"
	@echo "  make install                  - Install frontend dependencies"
	@echo "  make dev                      - Start both frontend and backend in development mode"
	@echo "  make hot                      - Start both backend and frontend development servers (recommended)"
	@echo "  make dev-frontend             - Start only frontend development server"
	@echo "  make dev-backend              - Start only backend development server"
	@echo "  make build                    - Build the project for production"
	@echo "  make clean                    - Clean build artifacts"
	@echo ""
	@echo "Presentation page commands:"
	@echo "  make presentation-install     - Install presentation page dependencies"
	@echo "  make presentation-dev         - Start presentation page development server"
	@echo "  make presentation-build       - Build presentation page for production"
	@echo "  make presentation-docker-build - Build presentation page Docker image"
	@echo "  make presentation-docker-up   - Start presentation page with Docker (port 3001)"
	@echo "  make presentation-docker-down - Stop presentation page Docker containers"
	@echo "  make presentation-docker-logs - Show presentation page Docker logs"

# Install dependencies
install:
	@echo "Installing frontend dependencies..."
	cd frontend-web && npm install

# Start both frontend and backend
dev:
	@echo "Starting development servers..."
	@echo "Backend will run on http://localhost:8008"
	@echo "Frontend will run on http://localhost:3000"
	@echo "Press Ctrl+C to stop both servers"
	@make -j2 dev-backend dev-frontend

# Start only frontend
dev-frontend:
	@echo "Starting frontend development server on port 3000..."
	cd frontend-web && npm start

# Start backend with integrated frontend (recommended)
dev-backend:
	@echo "Starting backend with integrated frontend..."
	cd backend && make hot-backend

# Start both backend and frontend development servers
hot:
	@echo "Starting both backend and frontend development servers..."
	cd backend && make hot

# Build for production
build:
	@echo "Building frontend for production..."
	cd frontend-web && npm run build
	@echo "Copying build files to backend..."
	rm -rf backend/dist
	cp -r frontend-web/build backend/dist
	@echo "Build complete!"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	cd frontend-web && rm -rf build node_modules/.cache
	rm -rf backend/dist

# Presentation page commands
presentation-install:
	@echo "Installing presentation page dependencies..."
	cd presentation_page && npm install

presentation-dev:
	@echo "Starting presentation page development server on port 3000..."
	cd presentation_page && npm start

presentation-build:
	@echo "Building presentation page for production..."
	cd presentation_page && npm run build

presentation-docker-build:
	@echo "Building presentation page Docker image..."
	cd presentation_page && docker build -t butce360-presentation .

presentation-docker-up:
	@echo "Starting presentation page with Docker..."
	@echo "Presentation page will be available at http://localhost:3001"
	cd presentation_page && docker-compose up -d

presentation-docker-down:
	@echo "Stopping presentation page Docker containers..."
	cd presentation_page && docker-compose down

presentation-docker-logs:
	@echo "Showing presentation page Docker logs..."
	cd presentation_page && docker-compose logs -f
